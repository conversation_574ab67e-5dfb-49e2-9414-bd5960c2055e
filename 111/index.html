<!DOCTYPE html>
<html lang="zh-CN" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- 引用主样式文件 -->
    <link rel="stylesheet" href="./static/css/main.css">
    <!-- 引用自定义样式文件 -->
    <link rel="stylesheet" href="./static/css/custom.css">
    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 引入现代字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Outfit:wght@300;400;500;600;700&family=Space+Grotesk:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500;700&display=swap" rel="stylesheet">
        <title>导航页</title>

</head>
<body class="scroll-hidden">
    <!-- 全局 Tooltip 系统 -->
    <div id="tooltip-overlay"></div>
    <div id="tooltip-container"></div>

    <!-- 全局弹窗系统 -->
    <div id="modal-overlay" class="hidden modal-overlay"></div>
    <div id="info-modal" class="hidden modal-content shadow-2xl rounded-lg">
        <div class="flex justify-between items-center px-6 pt-6 pb-3 border-b">
            <h2 class="text-2xl font-bold">重要信息</h2>
            <button id="modal-close" class="w-10 h-10 flex items-center justify-center rounded-full btn-secondary transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </button>
        </div>
        <div class="px-6 py-5 max-h-60vh overflow-y-auto space-y-6">
            <div>
                <h3 class="text-xl font-semibold mb-3">禅道测试流程</h3>
                <p class="text-base leading-relaxed">产品下单测试➡️<span class="text-emphasis-danger">开发服验收通过（包含用例）</span>➡️通知产品合并版本➡️<span class="text-emphasis-danger">测试服验收通过</span>➡️通知产品合并版本➡️<span class="text-emphasis-danger">交付服验收通过</span>➡️完成</p>
                <p class="mt-3 text-base leading-relaxed">1、禅道完成任务后，对应的环境要修改，DEV代表开发，ST代表测试，FIX代表交付<br>
                   2、3部无测试服流程，直接进入交付服流程（EGC和FG的测试后台即交付后台）
                </p>
            </div>
            <div>
                <h3 class="text-xl font-semibold mb-3">注意事项</h3>
                <p class="text-base leading-relaxed">1、皮肤套即原始大厅的套皮版本<br>
                   2、快捷查看当前是内存版的游戏厂商：<a href="https://gc-api.gcdev.link/api/index" target="_blank" class="text-emphasis-primary hover:underline">点击跳转</a>
                </p>
                <h3 class="text-lg font-semibold mt-4">内部游戏数据流：账服</h3>
                <h3 class="text-lg font-semibold mt-2">外部游戏数据流（包含FG-AAGAME）：EGC➡️账服</h3>
                <h3 class="text-lg font-semibold mt-2">FG游戏内IGC游戏数据流：IGC➡️EGC➡️账服</h3>
                <p class="mt-3 text-base leading-relaxed">1、FG后台数据延迟3分钟<br>
                   2、FG总台数据刷新：半点、整点（后10分钟）<br>
                   3、EGC转移钱包数据延迟15分钟<br>
                </p>
            </div>
        </div>

    </div>

    <!-- 日历弹窗 -->
    <div id="calendar-modal" class="hidden modal-content shadow-2xl rounded-lg">
        <div class="flex justify-between items-center px-6 pt-6 pb-3 border-b">
            <div class="flex items-center">
                <button id="prev-month" class="calendar-nav-btn mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="15 18 9 12 15 6"></polyline>
                    </svg>
                </button>
                <h2 id="calendar-month-year" class="text-2xl font-bold">2025年5月</h2>
                <button id="next-month" class="calendar-nav-btn ml-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="9 18 15 12 9 6"></polyline>
                    </svg>
                </button>
            </div>
            <button id="calendar-modal-close" class="w-10 h-10 flex items-center justify-center rounded-full btn-secondary transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </button>
        </div>
        <div class="px-6 py-5 max-h-60vh overflow-y-auto">
            <div class="calendar-container">
                <table class="calendar-table w-full border-collapse">
                    <thead>
                        <tr>
                            <th class="calendar-header">一</th>
                            <th class="calendar-header">二</th>
                            <th class="calendar-header">三</th>
                            <th class="calendar-header">四</th>
                            <th class="calendar-header">五</th>
                            <th class="calendar-header">六</th>
                            <th class="calendar-header">日</th>
                        </tr>
                    </thead>
                    <tbody id="calendar-days">
                        <!-- 日历日期将通过JavaScript动态生成 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 顶部标题栏 -->
    <header class="header-bar">
        <div class="header-container">
            <div class="flex items-center">
                <h1 class="header-title">🐟鲤鱼导航页</h1>
            </div>

            <!-- 搜索框 -->
            <div class="search-container">
                <form action="https://www.google.com/search" method="get" target="_blank" class="search-box">
                    <input type="text" id="search-input" name="q" class="search-input" placeholder="Google搜索..." aria-label="Google搜索">
                    <button type="submit" id="search-button" class="search-button" aria-label="搜索">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="11" cy="11" r="8"></circle>
                            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                        </svg>
                    </button>
                </form>
            </div>

            <!-- 按钮容器 - 为移动端优化 -->
            <div class="header-buttons-container">
                <!-- 记事本按钮 -->
                <button id="notepad-button" class="theme-toggle-header mr-2" aria-label="记事本">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                    </svg>
                </button>

                <!-- 日历按钮 -->
                <button id="calendar-button" class="theme-toggle-header mr-2" aria-label="日历">
                    <div class="calendar-button-content">
                        <div class="calendar-date-display">
                            <span id="current-date">--</span>
                            <span id="current-day">--</span>
                        </div>
                    </div>
                </button>

                <!-- 重要信息按钮 -->
                <button id="show-info-modal" class="theme-toggle-header mr-2" aria-label="重要信息">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="12" y1="16" x2="12" y2="12"></line>
                        <line x1="12" y1="8" x2="12.01" y2="8"></line>
                    </svg>
                </button>

                <!-- 主题切换按钮移动到标题栏右侧 -->
                <button id="theme-toggle" class="theme-toggle-header" aria-label="切换主题">
                    <svg id="theme-icon-light" class="theme-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="5"></circle>
                        <line x1="12" y1="1" x2="12" y2="3"></line>
                        <line x1="12" y1="21" x2="12" y2="23"></line>
                        <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                        <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                        <line x1="1" y1="12" x2="3" y2="12"></line>
                        <line x1="21" y1="12" x2="23" y2="12"></line>
                        <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                        <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                    </svg>
                    <svg id="theme-icon-dark" class="theme-icon hidden" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
                    </svg>
                </button>
            </div>
        </div>
    </header>

    <div class="outer-container scroll-hidden">

        <div class="left-column scroll-hidden">
            <div class="module">
                <h1 class="module-title">常用</h1>
                <div class="items">
                    <div class="item">
                        <h2>开发DEV</h2>
                        <div class="link-container">
                            <a href="http://*************:8090/oye2_india_10_dev/web-mobile/?f=UIHall" target="_blank">10套原始</a>
                            <a href="http://*************:8090/oye2_india_10_auth_dev/web-mobile/?f=UIHall" target="_blank">认证</a>
                            <a href="http://*************:8090/webqp_1_dev/dist/#/menu" target="_blank">web1</a>
                            <a href="http://*************:8090/webqp_2_dev/dist/#/menu" target="_blank">web2</a>
                            <a href="http://*************:8090/webqp_4_dev/dist/#/menu" target="_blank">web4</a>
                            <a href="http://*************:8000" target="_blank">web5</a>
                            <a href="https://dev-goc.bbadev.cc" target="_blank">cocos后台地址</a>
                            <a href="https://web-goc.bbadev.cc" target="_blank">web后台地址</a>
                            <a href="https://ck-goc.bbadev.cc/login/index.html" target="_blank">认证后台地址</a>
                        </div>
                    </div>
                    <div class="item">
                        <h2>测试ST</h2>
                        <div class="link-container">
                            <a href="http://*************:8090/oye2_india_10_st/web-mobile/?f=UIHall" target="_blank">10套原始</a>
                            <a href="http://*************:8090/webqp_1_pre_fix_st/dist/#/menu" target="_blank">web1</a>
                            <a href="http://*************:8090/webqp_2_pre_fix_st/dist/#/menu" target="_blank">web2</a>
                            <a href="https://test-goc.bbatest.cc/login/index.html" target="_blank">cocos后台地址</a>
                            <a href="https://web-goc.bbatest.cc/user/index.html" target="_blank">web后台地址</a>
                            
                        </div>
                    </div>
                    <div class="item">
                        <h2>交付FIX</h2>
                        <div class="link-container">
                            <a href="http://*************:8090/oye2_india_10_fix_st/web-mobile/?f=UIHall" target="_blank">10套原始</a>
                            <a href="http://*************:8090/oye2_india_10_auth_st/web-mobile/?f=UIHall" target="_blank">认证</a>
                            <a href="http://*************:8090/webqp_1_fix_st/dist/#/menu" target="_blank">web1</a>
                            <a href="http://*************:8090/webqp_2_fix_st/dist/#/menu" target="_blank">web2</a>
                            <a href="http://*************:8090/webqp_4_fix_st/dist/#/menu" target="_blank">web4</a>
                            <a href="http://*************:8001" target="_blank">web5</a>
                            <a href="https://10-goc.bbademo.cc" target="_blank">cocos后台地址</a>
                            <a href="https://web1-goc.bbademo.cc/index/index.html" target="_blank">web后台地址</a>
                            <a href="https://ck-goc.bbademo.cc/login/index.html" target="_blank">认证后台地址</a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="module">
                <h1 class="module-title">备忘录</h1>
                <div class="items">
                    <div class="item">
                        <h2>美术Figma</h2>
                        <div class="link-container">
                            <a href="https://www.figma.com/design/Rol7AbDaqijqFB1PjBknD4/website?node-id=0-1&p=f&m=dev" target="_blank">新官网前端</a>
                            <a href="https://www.figma.com/design/vGUINnRoT35SKlaQgcedCk/FG%E5%90%8E%E5%8F%B0%E7%B3%BB%E7%BB%9F?node-id=0-1&p=f&t=N57SAFqKiUjvfKBL-0" target="_blank">新官网后台</a>
                            <a href="https://www.figma.com/design/17Xzeq9zAHNJQ9DgQsIFZr/AI%E7%BF%BB%E8%AF%91?node-id=298-8000&t=hwkOsjA8kKQ9ABWo-0" target="_blank">松果翻译</a>
                            <a href="https://www.figma.com/design/HZt53PYLYemPOJQAzW0H2G/web5-cocos12?node-id=76-26&p=f&t=acM2aM64CviC0xix-0" target="_blank">web5</a>
                        </div>
                    </div>
                    <div class="item">
                        <h2>AI助理</h2>
                        <div class="link-container">
                            <a href="https://metaso.cn/" target="_blank">秘塔</a>
                            <a href="https://yuanbao.tencent.com/chat/naQivTmsDa" data-title="腾讯元宝" data-iframe="true" class="ai-assistant-link">腾讯元宝</a>
                            <a href="https://chat.deepseek.com/" target="_blank">DeepSeek</a>
                            <a href="https://grok.com/" target="_blank">Grok3</a>
                            <a href="https://api.v3.cm/register?aff=shn2" target="_blank">三方API</a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="module">
                                    <h1 class="module-title">开发服DEV</h1>
                <div class="items">
                    <div class="item">
                        <h2>大厅Cocos
                            <button class="info-btn" data-tooltip="游戏在这里看，自己注册账号，邮箱无需验证随便填写">📢</button>
                        </h2>
                        <div class="link-container">
                            <a href="http://*************:8090/oye2_india_10_dev/web-mobile/?f=UIHall" target="_blank">10套原始</a>
                            <a href="http://*************:8090/oye2_india_12_dev/web-mobile/?f=UIHall" target="_blank">12套原始</a>
                            <a href="http://*************:8090/oye2_india_13_dev/web-mobile/?f=UIHall" target="_blank">13套原始</a>
                            <a href="http://*************:8090/oye2_india_15_dev/web-mobile/?f=UIHall" target="_blank">15套原始</a>
                            <a href="http://*************:8090/oye2_india_10_auth_dev/web-mobile/?f=UIHall" target="_blank">认证</a>
                            <a href="http://*************:8090/oye2_india_9_dev/web-desktop/?f=UIHall" target="_blank">9套原始</a>
                        </div>
                    </div>
                    <div class="item">
                        <h2>大厅Web
                            <button class="info-btn" data-tooltip="游戏在这里看，自己注册账号，邮箱无需验证随便填写">📢</button>
                        </h2>
                        <div class="link-container">
                            <a href="http://*************:8090/webqp_1_dev/dist/#/menu" target="_blank">web1</a>
                            <a href="http://*************:8090/webqp_2_dev/dist/#/menu" target="_blank">web2</a>
                            <a href="http://*************:8090/webqp_4_dev/dist/#/home" target="_blank">web4</a>
                            <a href="http://**************:8000/home" target="_blank">web5</a>
                            <a href="http://*************:8090/webqp_25_dev/dist/#/home" target="_blank">web25</a>
                        </div>
                    </div>
                    <div class="item">
                        <h2>账服
                            <button class="info-btn" data-tooltip="控制大厅相关的设置，如加钱查账号等">📢</button>
                        </h2>
                        <div class="link-container">
                            <a href="https://dev-goc.bbadev.cc" target="_blank">cocos后台地址</a>
                            <a href="https://web-goc.bbadev.cc" target="_blank">web后台地址</a>
                            <a href="https://web4-goc.bbadev.cc" target="_blank">web4后台地址</a>
                            <a href="https://web25-goc.bbadev.cc/index/index.html" target="_blank">web25后台地址</a>
                            <a href="https://ck-goc.bbadev.cc/login/index.html" target="_blank">认证后台地址</a>
                            <a href="https://com-goc.bbadev.cc" target="_blank">9套后台地址</a>
                        </div>
                    </div>
                    <div class="item">
                        <h2>EGC/FG
                            <button class="info-btn" data-tooltip="EGC包含FG相关的数据,FG仅FG平台游戏的数据">📢</button>
                        </h2>
                        <div class="link-container">
                            <a href="https://gcdev-admin.gcdev.link" target="_blank">EGC后台地址</a>
                            <a href="https://fggcdev-admin.gcdev.link" target="_blank">FG后台地址</a>
                        </div>
                    </div>
                    <div class="item">
                        <h2>数据分析
                            <button class="info-btn" data-tooltip="也是看数据的">📢</button>
                        </h2>
                        <div class="link-container">
                            <a href="https://gcdev-report.gcdev.link" target="_blank">后台地址</a>
                        </div>
                    </div>
                    <div class="item">
                        <h2>账服（EGC）总台
                            <button class="info-btn" data-tooltip="也是看数据的">📢</button>
                        </h2>
                        <div class="link-container">
                            <a href="https://ms-pc.bbadev.cc/web/#/home" target="_blank">前端</a>
                            <a href="https://ms-pc.bbadev.cc/login/index.html" target="_blank">后台</a>
                        </div>
                    </div>
                    <div class="item">
                        <h2>新官网
                            <button class="info-btn" data-tooltip="江湖人称小账服<br>管理员账号：__admingogoB5<br>管理员密码：super888">📢</button>
                        </h2>
                        <div class="link-container">
                            <a href="http://**************:13000/" target="_blank">大厅地址</a>
                            <a href="https://fggw-dev-goc.bbqdev.cc" target="_blank">后台地址</a>
                        </div>
                    </div>
                    <div class="item">
                        <h2>商户后台
                            <button class="info-btn" data-tooltip="登录的是总代/商户/子商户账号">📢</button>
                        </h2>
                        <div class="link-container">
                            <a href="https://fggcdev-merchant.gcdev.link" target="_blank">开发后台</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="module">
                                    <h1 class="module-title">测试服ST</h1>
                <div class="items">
                    <div class="item">
                        <h2>大厅Cocos</h2>
                        <div class="link-container">
                            <a href="http://*************:8090/oye2_india_10_st/web-mobile/?f=UIHall" target="_blank">10套原始</a>
                            <a href="http://*************:8090/oye2_india_9_st/web-desktop/?f=UIHall" target="_blank">9套原始</a>
                        </div>
                    </div>
                    <div class="item">
                        <h2>大厅Web</h2>
                        <div class="link-container">
                            <a href="http://*************:8090/webqp_1_pre_fix_st/dist/#/menu" target="_blank">web1</a>
                            <a href="http://*************:8090/webqp_2_pre_fix_st/dist/#/menu" target="_blank">web2</a>
                            <a href="http://*************:8090/webqp_4_st/dist/#/menu" target="_blank">web4</a>
                            <a href="http://**************:8001/home" target="_blank">web5</a>
                        </div>
                    </div>
                    <div class="item">
                        <h2>账服</h2>
                        <div class="link-container">
                            <a href="https://test-goc.bbatest.cc/login/index.html" target="_blank">cocos后台地址</a>
                            <a href="https://web-goc.bbatest.cc/user/index.html" target="_blank">web后台地址</a>
                            <a href="https://com-goc.bbatest.cc/login/index.html" target="_blank">9套后台地址</a>
                        </div>
                    </div>
                    <div class="item">
                        <h2>EGC/FG（测试交付一样的）</h2>
                        <div class="link-container">
                            <a href="https://egc-goc.bbadev.cc/index/index.html" target="_blank">EGC后台地址</a>
                            <a href="https://fgegc-goc.bbadev.cc/login/index.html" target="_blank">FG后台地址</a>
                        </div>
                    </div>
                    <div class="item">
                        <h2>数据分析</h2>
                        <div class="link-container">
                            <a href="https://egc-report.bbadev.cc/login/index.html" target="_blank">后台地址</a>
                            <a href="https://fgegc-data.bbadev.cc/index/index.html" target="_blank">FG数据测试后台（10-123）</a>
                            <a href="https://egc-data.bbadev.cc/index/index.html" target="_blank">EGC数据测试后台（10-123）</a>
                        </div>
                    </div>
                    <div class="item">
                        <h2>账服（EGC）总台</h2>
                        <div class="link-container">
                            <a href="https://ms-pc.bbatest.cc/web/#/home" target="_blank">前端</a>
                            <a href="https://ms-pc.bbatest.cc/login/index.html" target="_blank">后台</a>
                        </div>
                    </div>
                    <div class="item">
                        <h2>新官网
                            <button class="info-btn" data-tooltip="管理员账号：__admingogoB5<br>管理员密码：super888">📢</button>
                        </h2>
                        <div class="link-container">
                            <a href="http://**************:3333" target="_blank">大厅地址</a>
                            <a href="https://fggw-pre-goc.bbqdev.cc" target="_blank">后台地址</a>
                        </div>
                    </div>
                    <div class="item">
                        <h2>商户后台
                            <button class="info-btn" data-tooltip="登录的是商户账号<br>商户账号：ztegc<br>商户密码：12345678">📢</button>
                        </h2>
                        <div class="link-container">
                            <a href="https://fgegc-merchant.bbadev.cc" target="_blank">测试（交付）后台</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="module">
                                    <h1 class="module-title">交付服FIX</h1>
                <div class="items">
                    <div class="item">
                        <h2>大厅Cocos</h2>
                        <div class="link-container">
                        <a href="http://*************:8090/oye2_india_10_fix_st/web-mobile/?f=UIHall" target="_blank">10套原始</a>
                        <a href="http://*************:8090/oye2_india_10_auth_st/web-mobile/?f=UIHall" target="_blank">认证</a>
                        <a href="http://*************:8090/oye2_india_10_fix1_st/web-mobile/?f=UIHall" target="_blank">10-1</a>
                        <a href="http://*************:8090/oye2_india_10_fix_2_st/web-mobile/?f=UIHall" target="_blank">10-2</a>
                        <a href="http://*************:8090/oye2_india_12_fix_2_st/web-mobile/?f=UIHall" target="_blank">10-3</a>
                        <a href="http://*************:8090/oye2_india_10_fix_5_st/web-mobile/?f=UIPromoteMain" target="_blank">10-5</a>
                        </div>
                    </div>
                    <div class="item">
                        <h2>大厅Web</h2>
                        <div class="link-container">
                        <a href="http://*************:8090/webqp_1_fix_st/dist/#/menu" target="_blank">web1</a>
                        <a href="http://*************:8090/webqp_2_fix_st/dist/#/menu" target="_blank">web2</a>
                        <a href="http://*************:8090/webqp_4_fix_st/dist/#/menu" target="_blank">web4</a>
                        <a href="http://**************:8002/home" target="_blank">web5</a>
                        <a href="http://*************:8090/webqp_25_fix_st/dist/#/home" target="_blank">web25</a>
                        </div>
                    </div>
                   
                    <div class="item">
                        <h2>账服</h2>
                        <div class="link-container">
                            <a href="https://10-goc.bbademo.cc" target="_blank">cocos后台地址</a>
                            <a href="https://web1-goc.bbademo.cc/index/index.html" target="_blank">web后台地址</a>
                            <a href="https://ck-goc.bbademo.cc/login/index.html" target="_blank">认证后台地址</a>
                            <a href="https://10-2-goc.bbademo.cc/login/index.html" target="_blank">10-2后台地址</a>
                            <a href="https://10-3-goc.bbademo.cc/login/index.html" target="_blank">10-3后台地址</a>
                            <a href="https://10-5-goc.bbademo.cc/login/index.html" target="_blank">10-5后台地址</a>
                            <a href="https://web25-goc.bbademo.cc/mguser/index.html" target="_blank">web25后台地址</a>
                        </div>
                    </div>
                     <div class="item">
                        <h2>大厅皮肤套
                            <button class="info-btn" data-tooltip="大厅的各种不同的皮肤">📢</button>
                        </h2>
                        <div class="link-container">
                            <a href="http://*************:8090/oye2_india_10_fix1_st/web-mobile/?f=UIHall" target="_blank">10-1</a>
                            <a href="http://*************:8090/oye2_india_10_fix4_st/web-mobile/?f=UIHall" target="_blank">10-4</a>
                            <a href="http://*************:8090/oye2_india_10_fix5_st/web-mobile/?f=UIHall" target="_blank">10-5</a>
                            <a href="http://*************:8090/oye2_india_12_fix1_st/web-mobile/?f=UIHall" target="_blank">12-1</a>
                            <a href="http://*************:8090/oye2_india_12_fix2_st/web-mobile/?f=UIHall" target="_blank">12-2</a>
                            <a href="http://*************:8090/oye2_india_12_fix3_st/web-mobile/?f=UIHall" target="_blank">12-3🌟</a>
                            <a href="http://*************:8090/oye2_india_12_fix4_st/web-mobile/?f=UIHall" target="_blank">12-4</a>
                            <a href="http://*************:8090/oye2_india_12_fix5_st/web-mobile/?f=UIHall" target="_blank">12-5</a>
                            <a href="http://*************:8090/oye2_india_13_fix1_st/web-mobile/?f=UIHall" target="_blank">13-1</a>
                            <a href="http://*************:8090/oye2_india_13_fix2_st/web-mobile/?f=UIHall" target="_blank">13-2🌟</a>
                            <a href="http://*************:8090/oye2_india_13_fix3_st/web-mobile/?f=UIHall" target="_blank">13-3</a>
                            <a href="http://*************:8090/oye2_india_15_fix1_st/web-mobile/?f=UIHall" target="_blank">15-1</a>
                            <a href="http://*************:8090/oye2_india_15_fix2_st/web-mobile/?f=UIHall" target="_blank">15-2</a>
                            <a href="http://*************:8090/oye2_india_15_fix3_st/web-mobile/?f=UIHall" target="_blank">15-3🌟</a>
                            <a href="http://*************:8090/oye2_india_15_fix4_st/web-mobile/?f=UIHall" target="_blank">15-4</a>
                            <a href="http://*************:8090/oye2_india_15_fix5_st/web-mobile/?f=UIHall" target="_blank">15-5</a>
                            <a href="http://*************:8090/webqp_1_fix1_st/dist/#/menu" target="_blank">web1-1</a>
                            <a href="http://*************:8090/webqp_1_fix2_st/dist/#/menu" target="_blank">web1-2</a>
                            <a href="http://*************:8090/webqp_2_fix1_st/dist/#/menu" target="_blank">web2-1</a>
                            <a href="http://*************:8090/webqp_2_fix2_st/dist/#/menu" target="_blank">web2-2</a>
                            <a href="http://*************:8090/webqp_2_fix3_st/dist/#/menu" target="_blank">web2-3</a>
                            <a href="http://*************:8090/webqp_2_fix4_st/dist/#/menu" target="_blank">web2-4</a>
                            <a href="http://*************:8090/webqp_2_fix5_st/dist/#/menu" target="_blank">web2-5</a>
                        </div>
                    </div>
                    <div class="item">
                        <h2>EGC/FG（测试交付一样的）</h2>
                        <div class="link-container">
                            <a href="https://egc-goc.bbadev.cc/index/index.html" target="_blank">EGC后台地址</a>
                            <a href="https://fgegc-goc.bbadev.cc/login/index.html" target="_blank">FG后台地址</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="module">
                                    <h1 class="module-title">其他后台</h1>
                <div class="items">
                    <div class="item">
                        <h2>试玩大厅</h2>
                        <div class="link-container">
                            <a href="http://fggcdev-demo.gcdev.link/web/#/game" target="_blank">FG试玩大厅开发服大厅</a>
                            <a href="https://igc-sw-pre-2-down.bbqdev.cc/web-fg/#/game" target="_blank">FG试玩大厅测试服大厅</a>
                            <a href="https://gcdev-demo.gcdev.link/web/#/game" target="_blank">试玩大厅开发大厅</a>
                        </div>
                    </div>
                    <div class="item">
                        <h2>FG总台</h2>
                        <div class="link-container">
                            <a href="https://fgreportapi-admin.gcdev.link/fg_web/#/login" target="_blank">开发后台地址</a>
                            <a href="https://fgreportapi.bbadev.cc/fg_web/#/login" target="_blank">测试后台地址</a>
                        </div>
                    </div>
                    <div class="item">
                        <h2>FG旧官网</h2>
                        <div class="link-container">
                            <a href="https://fg-dev-home.bbqdev.cc" target="_blank">开发前端</a>
                            <a href="https://fg-dev-goc.bbqdev.cc" target="_blank">开发后台</a>
                            <a href="https://fg-pre-home.bbqdev.cc" target="_blank">交付前端</a>
                            <a href="https://fg-pre-goc.bbqdev.cc" target="_blank">交付后台</a>
                        </div>
                    </div>
                    <div class="item">
                        <h2>CPF</h2>
                        <div class="link-container">
                            <a href="https://admin-cpf-dev.bbadev.cc/#/supplierManage/statement" target="_blank">CPF开发后台地址</a>
                            <a href="https://admin-cpf-pre.bbademo.cc/#/supplierManage/statement" target="_blank">CPF交付后台地址</a>
                            <a href="https://merchant-cpf-dev.bbadev.cc/#/login?redirect=/merchantManage/statement" target="_blank">开发商户CPF后台地址</a>
                        </div>
                    </div>
                    <div class="item">
                        <h2>客服相关</h2>
                        <div class="link-container">
                            <a href="https://kfms-dev-goc.bbadev.cc/web/#/index" target="_blank">客服总台开发</a>
                            <a href="https://kfms-pre-goc.bbadev.cc/web/#/index" target="_blank">客服总台交付</a>
                        </div>
                    </div>
                    <div class="item">
                        <h2>IGC</h2>
                        <div class="link-container">
                            <a href="https://devigc-igc.bbadev.cc/mguser/index.html" target="_blank">IGC总台</a>
                            <a href="https://igc-sw-igc.bbqdev.cc/login/index.html" target="_blank">开发服</a>
                            <a href="https://igc-sw-pre-igc.bbqdev.cc/login/index.html" target="_blank">测试服</a>
                        </div>
                    </div>
                    <div class="item">
                        <h2>工单系统</h2>
                        <div class="link-container">
                            <a href="https://fg-wos.bbqdev.cc" target="_blank">开发后台</a>
                            <a href="https://fg-pre-wos.bbqdev.cc" target="_blank">交付后台</a>
                        </div>
                    </div>

                    <div class="item">
                        <h2>静态官网</h2>
                        <div class="link-container">
                            <a href="https://lab-pre.bbqdev.cc/#/home" target="_blank">实验室</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="automation-tools-column" class="right-column scroll-hidden">
            <!-- 辅助工具模块 -->
            <div class="module">
                <h1 class="module-title auxiliary-module-title">辅助工具</h1>
                <div class="items py-2">
                    <div class="aux-grid">
                        <a href="https://cd.baa360.cc:20088/index.php?m=my&f=index" target="_blank" class="aux-link">
                            <span class="aux-link-icon">📊</span>
                            <span class="aux-link-text">禅道</span>
                        </a>
                        <a href="https://cd.baa360.cc:20088/index.php?m=report&f=onlinebug&lastWeek=0&thisWeek=1&begin=&end=&isai=0&ids=&envType=0&c_begin=2025-02-11&c_end=2025-04-22&dept=0&subtype=&deptSearch=&scheduleStatus=&openedBy=&t=html#app=my" target="_blank" class="aux-link">
                            <span class="aux-link-icon">📊</span>
                            <span class="aux-link-text">禅道客户反馈</span>
                        </a>
                        <a href="http://192.168.31.75:8082/" target="_blank" class="aux-link">
                            <span class="aux-link-icon">🔨</span>
                            <span class="aux-link-text">Jenkins构建导航</span>
                        </a>
                        <a href="http://192.168.23.35:8091/project/test" target="_blank" class="aux-link">
                            <span class="aux-link-icon">🧪</span>
                            <span class="aux-link-text">自动化测试平台</span>
                        </a>
                        <a href="./static/game/贪吃蛇/开始游戏.html" target="_blank" class="aux-link">
                            <span class="aux-link-icon">🐍</span>
                            <span class="aux-link-text">贪吃的宇楠</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 谷歌文档模块 -->
            <div class="module">
                <h1 class="module-title">谷歌文档</h1>
                <div class="items">
                    <div class="item">
                        <h2>文档链接</h2>
                        <div class="link-container">
                            <a href="https://docs.google.com/spreadsheets/d/1IGj1gIpaIDReUHCbemZpww0brmHa22YYm5uRjum2dnE/edit?gid=393060471#gid=393060471" target="_blank">web5.0问题汇总</a>
                            <a href="https://docs.google.com/spreadsheets/d/1cD6pBYlyGvNZCjOVeJIzRArtR7FqSuQWRhlXJagaSyY/edit?gid=294771485#gid=294771485" target="_blank">一部QA日报</a>
                            <a href="https://docs.google.com/spreadsheets/d/1VJ33mL_ddAFZfI7niLm5iUIOLuDRf-QqmSdXZI7vOFU/edit?gid=1149989571#gid=1149989571" target="_blank">一部主要事项</a>
                            <a href="https://docs.google.com/spreadsheets/d/1TYMTiHqWdMFdJti79eNnM_aqhLnMlrs9mBjc9shcSFA/edit?gid=286280307#gid=286280307" target="_blank">三部主要事项</a>
                            <a href="https://docs.google.com/spreadsheets/d/1QlmmKi7lK5Xw6jT-tG611_bayl6QExXNVh51kHbEc1E/edit?gid=823252870#gid=823252870" target="_blank">版本交付</a>
                            <a href="https://docs.google.com/spreadsheets/d/13C0_kaOy8wo0tdFiyl7v9si0Ai3551VpH4cBmO57lwE/edit?gid=1559542566#gid=1559542566" target="_blank">EGC任务总表</a>
                            <a href="https://docs.google.com/spreadsheets/d/1Gfli-kPMO06D42x7XTH275eGo_3VFIbXCeaIewzfYHo/edit?gid=2023098010#gid=2023098010" target="_blank">FG官网需求池</a>
                            <a href="https://docs.google.com/spreadsheets/d/1eafWP18iWnoAIsrabY9WWJ2q_uBM_YsK3yfUKYDlcFw/edit?gid=724226873#gid=724226873" target="_blank">单一游戏</a>
                        </div>
                    </div>
                </div>
            </div>



        </div>
    </div>

    <!-- 返回顶部按钮 -->
    <button id="back-to-top" class="back-to-top" title="返回顶部">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="18 15 12 9 6 15"></polyline>
        </svg>
    </button>

    <div id="output-container" class="scroll-hidden" style="display: none;">
        <button class="close-button" title="关闭">×</button>
        <h2>输出结果</h2>
        <pre id="output" class="scroll-hidden"></pre>
    </div>

    <!-- 内嵌网页弹窗 -->
    <div id="iframe-modal" class="hidden modal-overlay" style="z-index: 10000;">
        <div class="iframe-modal-content">
            <div class="iframe-header">
                <h2 id="iframe-title" class="iframe-title">AI助理</h2>
                <div class="iframe-controls">
                    <button id="iframe-refresh" class="iframe-btn" title="刷新">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"></path>
                            <path d="M21 3v5h-5"></path>
                            <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"></path>
                            <path d="M3 21v-5h5"></path>
                        </svg>
                    </button>
                    <button id="iframe-external" class="iframe-btn" title="在新窗口打开">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M15 3h6v6"></path>
                            <path d="M10 14 21 3"></path>
                            <rect x="2" y="2" width="8" height="8"></rect>
                        </svg>
                    </button>
                    <button id="iframe-close" class="iframe-btn" title="关闭">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                    </button>
                </div>
            </div>
            <div class="iframe-body">
                <div id="iframe-loading" class="iframe-loading">
                    <div class="loading-spinner"></div>
                    <div class="loading-text">正在加载...</div>
                </div>
                <iframe id="iframe-content" class="iframe-content" src="" frameborder="0" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-top-navigation"></iframe>
            </div>
        </div>
    </div>

    <!-- 记事本弹窗 -->
    <div id="notepad-container" class="hidden w-500 h-400 max-w-90vw max-h-80vh notepad-visible">
        <div class="flex justify-between items-center px-6 pt-6 pb-3 border-b">
            <h2 class="text-2xl font-bold">记事本</h2>
            <div class="flex space-x-2">
                <button id="notepad-save" class="w-10 h-10 flex items-center justify-center rounded-full btn-secondary transition-colors" title="保存">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                        <polyline points="17 21 17 13 7 13 7 21"></polyline>
                        <polyline points="7 3 7 8 15 8"></polyline>
                    </svg>
                </button>
                <button id="notepad-clear" class="w-10 h-10 flex items-center justify-center rounded-full btn-secondary transition-colors" title="清空">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="3 6 5 6 21 6"></polyline>
                        <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                    </svg>
                </button>
                <button id="notepad-close" class="w-10 h-10 flex items-center justify-center rounded-full btn-secondary transition-colors" title="关闭">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
        </div>
        <div class="flex-1 px-6 py-4 overflow-hidden">
            <textarea id="notepad-text" class="w-full h-full form-input font-mono text-base leading-relaxed" placeholder="在这里输入你的笔记..."></textarea>
        </div>
    </div>



    <!-- 内容区域装饰花朵 -->
    <div class="flower-decoration flower-content-1">
        <img src="./static/svg/small-flower.svg" alt="装饰花朵" class="flower-svg small-flower">
    </div>
    <div class="flower-decoration flower-content-2">
        <img src="./static/svg/small-flower.svg" alt="装饰花朵" class="flower-svg small-flower">
    </div>
    <div class="flower-decoration flower-content-3">
        <img src="./static/svg/small-flower.svg" alt="装饰花朵" class="flower-svg small-flower">
    </div>

    <!-- JavaScript 文件引用 -->
    <!-- 通用工具函数 -->
    <script src="./static/js/utils.js"></script>

    <!-- 主题切换功能 -->
    <script src="./static/js/theme.js"></script>

    <!-- Tooltip 系统 -->
    <script src="./static/js/tooltip.js"></script>

    <!-- 弹窗系统 -->
    <script src="./static/js/modal.js"></script>

    <!-- 日历功能 -->
    <script src="./static/js/calendar.js"></script>

    <!-- 记事本功能 -->
    <script src="./static/js/notepad.js"></script>

    <!-- 内嵌网页模态窗口功能 -->
    <script src="./static/js/iframe-modal.js"></script>

    <!-- UI交互功能 -->
    <script src="./static/js/ui-interactions.js"></script>
</body>
</html>
