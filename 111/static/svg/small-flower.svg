<?xml version="1.0" encoding="UTF-8"?>
<svg width="50" height="50" viewBox="0 0 50 50" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="smallPetalGradient" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#E6E6FA" stop-opacity="0.9"/>
      <stop offset="100%" stop-color="#9370DB" stop-opacity="0.7"/>
    </radialGradient>
  </defs>
  <!-- 小花瓣 -->
  <g transform="translate(25, 25)">
    <path d="M0,-15 C5,-10 10,-5 0,0 C-10,-5 -5,-10 0,-15" fill="url(#smallPetalGradient)" transform="rotate(0)">
      <animateTransform attributeName="transform" type="rotate" from="0" to="360" dur="15s" repeatCount="indefinite" />
    </path>
    <path d="M0,-15 C5,-10 10,-5 0,0 C-10,-5 -5,-10 0,-15" fill="url(#smallPetalGradient)" transform="rotate(72)">
      <animateTransform attributeName="transform" type="rotate" from="72" to="432" dur="15s" repeatCount="indefinite" />
    </path>
    <path d="M0,-15 C5,-10 10,-5 0,0 C-10,-5 -5,-10 0,-15" fill="url(#smallPetalGradient)" transform="rotate(144)">
      <animateTransform attributeName="transform" type="rotate" from="144" to="504" dur="15s" repeatCount="indefinite" />
    </path>
    <path d="M0,-15 C5,-10 10,-5 0,0 C-10,-5 -5,-10 0,-15" fill="url(#smallPetalGradient)" transform="rotate(216)">
      <animateTransform attributeName="transform" type="rotate" from="216" to="576" dur="15s" repeatCount="indefinite" />
    </path>
    <path d="M0,-15 C5,-10 10,-5 0,0 C-10,-5 -5,-10 0,-15" fill="url(#smallPetalGradient)" transform="rotate(288)">
      <animateTransform attributeName="transform" type="rotate" from="288" to="648" dur="15s" repeatCount="indefinite" />
    </path>
    
    <!-- 花蕊 -->
    <circle cx="0" cy="0" r="3" fill="#FFFFFF">
      <animate attributeName="r" values="3;3.5;3" dur="2s" repeatCount="indefinite" />
    </circle>
  </g>
</svg>
