/**
 * UI交互功能
 */
document.addEventListener('DOMContentLoaded', function() {
    // 为所有模块和项目添加滚动隐藏类
    document.querySelectorAll('.module, .items, .item').forEach(el => {
        el.classList.add('scroll-hidden');
    });

    // 强制隐藏滚动条 - JavaScript 方案
    function forceHideScrollbars() {
        // 创建样式元素
        const style = document.createElement('style');
        style.textContent = `
            /* 强制隐藏所有滚动条 - 最高优先级 */
            html, body, * {
                scrollbar-width: none !important;
                -ms-overflow-style: none !important;
            }

            html::-webkit-scrollbar,
            body::-webkit-scrollbar,
            *::-webkit-scrollbar,
            ::-webkit-scrollbar {
                display: none !important;
                width: 0 !important;
                height: 0 !important;
                background: transparent !important;
            }

            /* 确保页面可以滚动但不显示滚动条 */
            html {
                overflow-x: hidden !important;
                scroll-behavior: smooth !important;
            }

            body {
                overflow-x: hidden !important;
                overflow-y: auto !important;
                scroll-behavior: smooth !important;
            }
        `;
        document.head.appendChild(style);
    }

    // 立即执行
    forceHideScrollbars();

    // 延迟执行确保覆盖其他样式
    setTimeout(forceHideScrollbars, 100);
    setTimeout(forceHideScrollbars, 500);

    // 添加滚动效果，使标题栏在滚动时变化
    const headerBar = document.querySelector('.header-bar');
    if (headerBar) {
        window.addEventListener('scroll', function() {
            if (window.scrollY > 20) {
                headerBar.classList.add('scrolled');
            } else {
                headerBar.classList.remove('scrolled');
            }
        });
    }

    // 返回顶部按钮功能
    const backToTopBtn = document.getElementById('back-to-top');

    // 滚动监听，显示/隐藏返回顶部按钮
    window.addEventListener('scroll', function() {
        if (window.scrollY > 300) {
            backToTopBtn.classList.add('visible');
        } else {
            backToTopBtn.classList.remove('visible');
        }
    });

    // 点击返回顶部 - 增强平滑滚动
    backToTopBtn.addEventListener('click', function() {
        // 使用更平滑的滚动动画
        const scrollToTop = () => {
            const currentScroll = window.pageYOffset;
            if (currentScroll > 0) {
                window.requestAnimationFrame(scrollToTop);
                window.scrollTo(0, currentScroll - (currentScroll / 8));
            }
        };

        // 优先使用原生平滑滚动，如果不支持则使用自定义动画
        if ('scrollBehavior' in document.documentElement.style) {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        } else {
            scrollToTop();
        }
    });

    // 增强滚动体验 - 添加滚动惯性
    let isScrolling = false;
    window.addEventListener('scroll', function() {
        if (!isScrolling) {
            window.requestAnimationFrame(function() {
                // 这里可以添加滚动时的额外效果
                isScrolling = false;
            });
            isScrolling = true;
        }
    });

    // 初始化时检查滚动位置，确保按钮状态正确
    if (window.scrollY > 300) {
        backToTopBtn.classList.add('visible');
    }

    // 移除了折叠/展开功能

    // 为所有链接添加微妙的悬停动画
    const links = document.querySelectorAll('.link-container a');
    links.forEach(link => {
        link.addEventListener('mouseenter', function() {
            this.style.transition = 'all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)';
        });

        link.addEventListener('mouseleave', function() {
            this.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        });
    });

    // 移除了随机渐变背景功能

    // 移除了折叠/展开初始化功能

    // 为所有additional-links的h4添加active类切换
    document.querySelectorAll('.additional-links h4').forEach(header => {
        header.addEventListener('click', function() {
            this.classList.toggle('active');
            // 切换下面链接的显示状态
            let links = this.nextElementSibling;
            while(links && links.tagName === 'A') {
                links.style.display = links.style.display === 'none' ? 'block' : 'none';
                links = links.nextElementSibling;
            }
        });
    });

    // 为所有link-container的h3添加active类切换
    document.querySelectorAll('.link-container h3').forEach(header => {
        // 创建一个数组来存储每个h3下面的链接
        const links = [];
        let nextElement = header.nextElementSibling;
        while(nextElement && nextElement.tagName === 'A') {
            links.push(nextElement);
            nextElement = nextElement.nextElementSibling;
        }

        // 为h3添加点击事件
        header.addEventListener('click', function() {
            this.classList.toggle('active');

            // 切换所有相关链接的显示状态
            const isActive = this.classList.contains('active');
            links.forEach(link => {
                // 如果是重要链接，始终显示
                if (link.classList.contains('block')) {
                    // 保持block类的链接始终显示
                    link.style.display = 'block';
                } else {
                    // 否则根据激活状态显示或隐藏
                    link.style.display = isActive ? 'block' : 'none';
                }
            });
        });

        // 初始化时隐藏所有非重要链接
        links.forEach(link => {
            if (!link.classList.contains('block')) {
                link.style.display = 'none';
            }
        });
    });

    // 修改link-container的展开收起功能，支持h2和h3
    document.querySelectorAll('.link-container').forEach(container => {
        // 处理所有标题元素(h2和h3)
        const headers = container.querySelectorAll('h2, h3');

        headers.forEach(header => {
            header.addEventListener('click', () => {
                // 获取该标题后面直到下一个标题之前的所有链接
                let links = [];
                let currentElement = header.nextElementSibling;

                while (currentElement && !['H2', 'H3'].includes(currentElement.tagName)) {
                    if (currentElement.tagName === 'A') {
                        links.push(currentElement);
                    }
                    currentElement = currentElement.nextElementSibling;
                }

                // 切换链接的显示状态
                const firstLink = links[0];
                if (firstLink) {
                    const isHidden = window.getComputedStyle(firstLink).display === 'none';
                    links.forEach(link => {
                        link.style.display = isHidden ? 'inline-block' : 'none';
                    });

                    // 如果在自动化工具模块中，调整模块高度
                    if (container.closest('#automation-tools-column')) {
                        adjustAutomationToolsHeight();
                    }
                }
            });
        });
    });

    // 调整自动化工具模块高度的函数
    function adjustAutomationToolsHeight() {
        const automationColumn = document.getElementById('automation-tools-column');
        if (!automationColumn) return;

        // 获取所有可见链接的数量
        const visibleLinks = automationColumn.querySelectorAll('.script-link[style*="display: inline-block"], .script-link[style*="display:inline-block"], .script-link.block, .script-link[style*="display: block"], .script-link[style*="display:block"]');

        // 如果展开的链接较多，增加容器高度
        if (visibleLinks.length > 5) {
            // 计算合适的高度，确保表单始终可见
            const formHeight = document.getElementById('upload-form')?.offsetHeight || 0;
            const headerHeight = automationColumn.querySelector('h1')?.offsetHeight || 0;
            const linksHeight = Array.from(visibleLinks).reduce((total, link) => total + link.offsetHeight, 0);
            const h3Elements = automationColumn.querySelectorAll('h3');
            const h3Height = Array.from(h3Elements).reduce((total, h3) => total + h3.offsetHeight, 0);
            const h2Height = automationColumn.querySelector('h2')?.offsetHeight || 0;

            // 计算所需的总高度，加上一些额外空间
            const totalHeight = headerHeight + h2Height + h3Height + linksHeight + formHeight + 100;

            // 设置最大高度，但不超过视口高度的90%
            const maxHeight = Math.min(totalHeight, window.innerHeight * 0.9);
            automationColumn.style.maxHeight = `${maxHeight}px`;
        } else {
            // 恢复默认高度
            automationColumn.style.maxHeight = 'calc(100vh - 80px)';
        }
    }

    // 页面加载时调整高度
    window.addEventListener('load', adjustAutomationToolsHeight);

    // 窗口大小改变时调整高度
    window.addEventListener('resize', adjustAutomationToolsHeight);
});
