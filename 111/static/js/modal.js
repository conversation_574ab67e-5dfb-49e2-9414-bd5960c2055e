/**
 * 信息弹窗系统
 */
document.addEventListener('DOMContentLoaded', function() {
    const infoModal = document.getElementById('info-modal');
    const modalOverlay = document.getElementById('modal-overlay');
    const modalClose = document.getElementById('modal-close');
    const showInfoModalBtn = document.getElementById('show-info-modal');

    // 显示弹窗
    function showModal() {
        // 确保初始状态正确
        infoModal.style.opacity = '0';
        modalOverlay.style.opacity = '0';

        infoModal.classList.remove('hidden');
        modalOverlay.classList.remove('hidden');

        // 使用淡入动画
        setTimeout(() => {
            infoModal.style.opacity = '1';
            modalOverlay.style.opacity = '1';
        }, 10);
    }

    // 隐藏弹窗
    function hideModal() {
        // 使用淡出动画
        infoModal.style.opacity = '0';
        modalOverlay.style.opacity = '0';

        // 等待动画完成后隐藏
        setTimeout(() => {
            infoModal.classList.add('hidden');
            modalOverlay.classList.add('hidden');
        }, 250);
    }

    // 关闭按钮点击事件
    modalClose.addEventListener('click', () => {
        hideModal();
    });

    // 点击遮罩层关闭弹窗
    modalOverlay.addEventListener('click', () => {
        hideModal();
    });

    // 按ESC键关闭弹窗
    document.addEventListener('keydown', e => {
        if (e.key === 'Escape' && !infoModal.classList.contains('hidden')) {
            hideModal();
        }
    });

    // 手动显示弹窗按钮
    if (showInfoModalBtn) {
        showInfoModalBtn.addEventListener('click', () => {
            showModal();
        });
    }
});
