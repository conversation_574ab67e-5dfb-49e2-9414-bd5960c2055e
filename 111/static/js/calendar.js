//日历功能
document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const calendarModal = document.getElementById('calendar-modal');
    const modalOverlay = document.getElementById('modal-overlay');
    const calendarButton = document.getElementById('calendar-button');
    const calendarModalClose = document.getElementById('calendar-modal-close');
    const currentDateEl = document.getElementById('current-date');
    const currentDayEl = document.getElementById('current-day');
    const calendarMonthYear = document.getElementById('calendar-month-year');
    const calendarDays = document.getElementById('calendar-days');
    const prevMonthBtn = document.getElementById('prev-month');
    const nextMonthBtn = document.getElementById('next-month');

    // 当前日期和显示的月份
    const today = new Date();
    let currentMonth = today.getMonth();
    let currentYear = today.getFullYear();

    // 星期几的中文名称
    const weekdays = ['一', '二', '三', '四', '五', '六', '日'];

    // 月份的中文名称
    const months = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'];

    // 农历数据 - 1900-2100年的农历信息
    const lunarInfo = [
        0x04bd8, 0x04ae0, 0x0a570, 0x054d5, 0x0d260, 0x0d950, 0x16554, 0x056a0, 0x09ad0, 0x055d2,
        0x04ae0, 0x0a5b6, 0x0a4d0, 0x0d250, 0x1d255, 0x0b540, 0x0d6a0, 0x0ada2, 0x095b0, 0x14977,
        0x04970, 0x0a4b0, 0x0b4b5, 0x06a50, 0x06d40, 0x1ab54, 0x02b60, 0x09570, 0x052f2, 0x04970,
        0x06566, 0x0d4a0, 0x0ea50, 0x06e95, 0x05ad0, 0x02b60, 0x186e3, 0x092e0, 0x1c8d7, 0x0c950,
        0x0d4a0, 0x1d8a6, 0x0b550, 0x056a0, 0x1a5b4, 0x025d0, 0x092d0, 0x0d2b2, 0x0a950, 0x0b557,
        0x06ca0, 0x0b550, 0x15355, 0x04da0, 0x0a5d0, 0x14573, 0x052d0, 0x0a9a8, 0x0e950, 0x06aa0,
        0x0aea6, 0x0ab50, 0x04b60, 0x0aae4, 0x0a570, 0x05260, 0x0f263, 0x0d950, 0x05b57, 0x056a0,
        0x096d0, 0x04dd5, 0x04ad0, 0x0a4d0, 0x0d4d4, 0x0d250, 0x0d558, 0x0b540, 0x0b5a0, 0x195a6,
        0x095b0, 0x049b0, 0x0a974, 0x0a4b0, 0x0b27a, 0x06a50, 0x06d40, 0x0af46, 0x0ab60, 0x09570,
        0x04af5, 0x04970, 0x064b0, 0x074a3, 0x0ea50, 0x06b58, 0x055c0, 0x0ab60, 0x096d5, 0x092e0,
        0x0c960, 0x0d954, 0x0d4a0, 0x0da50, 0x07552, 0x056a0, 0x0abb7, 0x025d0, 0x092d0, 0x0cab5,
        0x0a950, 0x0b4a0, 0x0baa4, 0x0ad50, 0x055d9, 0x04ba0, 0x0a5b0, 0x15176, 0x052b0, 0x0a930,
        0x07954, 0x06aa0, 0x0ad50, 0x05b52, 0x04b60, 0x0a6e6, 0x0a4e0, 0x0d260, 0x0ea65, 0x0d530,
        0x05aa0, 0x076a3, 0x096d0, 0x04bd7, 0x04ad0, 0x0a4d0, 0x1d0b6, 0x0d250, 0x0d520, 0x0dd45,
        0x0b5a0, 0x056d0, 0x055b2, 0x049b0, 0x0a577, 0x0a4b0, 0x0aa50, 0x1b255, 0x06d20, 0x0ada0,
        0x14b63, 0x09370, 0x049f8, 0x04970, 0x064b0, 0x168a6, 0x0ea50, 0x06b20, 0x1a6c4, 0x0aae0,
        0x0a2e0, 0x0d2e3, 0x0c960, 0x0d557, 0x0d4a0, 0x0da50, 0x05d55, 0x056a0, 0x0a6d0, 0x055d4,
        0x052d0, 0x0a9b8, 0x0a950, 0x0b4a0, 0x0b6a6, 0x0ad50, 0x055a0, 0x0aba4, 0x0a5b0, 0x052b0,
        0x0b273, 0x06930, 0x07337, 0x06aa0, 0x0ad50, 0x14b55, 0x04b60, 0x0a570, 0x054e4, 0x0d160,
        0x0e968, 0x0d520, 0x0daa0, 0x16aa6, 0x056d0, 0x04ae0, 0x0a9d4, 0x0a2d0, 0x0d150, 0x0f252,
        0x0d520
    ];

    // 农历基准日期：1900年1月31日为农历1900年正月初一
    const lunarBaseDate = new Date(1900, 0, 31);

    // 农历月份名称
    const lunarMonths = ['正', '二', '三', '四', '五', '六', '七', '八', '九', '十', '冬', '腊'];

    // 农历日期名称
    const lunarDays = [
        '初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十',
        '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
        '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十'
    ];

    // 2025年法定节假日数据
    const holidays = {
        '1-1': { name: '元旦', isHoliday: true },
        '2-14': { name: '情人节', isHoliday: false },
        '3-8': { name: '妇女节', isHoliday: false },
        '3-12': { name: '植树节', isHoliday: false },
        '4-1': { name: '愚人节', isHoliday: false },
        '4-4': { name: '清明节', isHoliday: true },
        '4-5': { name: '清明节', isHoliday: true },
        '4-6': { name: '清明节', isHoliday: true },
        '5-1': { name: '劳动节', isHoliday: true },
        '5-2': { name: '劳动节', isHoliday: true },
        '5-3': { name: '劳动节', isHoliday: true },
        '5-4': { name: '青年节', isHoliday: false },
        '5-31': { name: '端午节', isHoliday: true },
        '6-1': { name: '儿童节', isHoliday: false },
        '10-1': { name: '国庆节', isHoliday: true },
        '10-2': { name: '国庆节', isHoliday: true },
        '10-3': { name: '国庆节', isHoliday: true },
        '10-4': { name: '国庆节', isHoliday: true },
        '10-5': { name: '国庆节', isHoliday: true },
        '10-6': { name: '中秋节', isHoliday: true },
        '10-7': { name: '国庆节', isHoliday: true },
        '10-8': { name: '国庆节', isHoliday: true },
        '12-25': { name: '圣诞节', isHoliday: false }
    };

    // 传统节日（农历）- 2025年对应的公历日期
    const traditionalHolidays = {
        '1-29': { name: '春节', isHoliday: true, lunar: true },  // 农历正月初一
        '1-30': { name: '春节', isHoliday: true, lunar: true },  // 农历正月初二
        '1-31': { name: '春节', isHoliday: true, lunar: true },  // 农历正月初三
        '2-1': { name: '春节', isHoliday: true, lunar: true },   // 农历正月初四
        '2-2': { name: '春节', isHoliday: true, lunar: true },   // 农历正月初五
        '2-3': { name: '春节', isHoliday: true, lunar: true },   // 农历正月初六
        '2-4': { name: '春节', isHoliday: true, lunar: true },   // 农历正月初七
        '2-12': { name: '元宵节', isHoliday: false, lunar: true }, // 农历正月十五
        '5-31': { name: '端午节', isHoliday: true, lunar: true }, // 农历五月初五
        '7-7': { name: '七夕节', isHoliday: false, lunar: true },
        '9-9': { name: '重阳节', isHoliday: false, lunar: true }
    };

    // 二十四节气 2025年
    const solarTerms = {
        '1-5': '小寒',
        '1-20': '大寒',
        '2-3': '立春',
        '2-18': '雨水',
        '3-5': '惊蛰',
        '3-20': '春分',
        '4-4': '清明',
        '4-20': '谷雨',
        '5-5': '立夏',
        '5-21': '小满',
        '6-5': '芒种',
        '6-21': '夏至',
        '7-7': '小暑',
        '7-22': '大暑',
        '8-7': '立秋',
        '8-23': '处暑',
        '9-7': '白露',
        '9-23': '秋分',
        '10-8': '寒露',
        '10-23': '霜降',
        '11-7': '立冬',
        '11-22': '小雪',
        '12-7': '大雪',
        '12-21': '冬至'
    };

    // 初始化日历按钮显示当前日期
    function initCalendarButton() {
        const month = today.getMonth() + 1; // 月份从0开始，需要+1
        const date = today.getDate();
        const day = weekdays[today.getDay() === 0 ? 6 : today.getDay() - 1]; // 调整为周一开始

        // 显示完整日期格式：月-日
        currentDateEl.textContent = `${month}月${date}日`;
        currentDayEl.textContent = `星期${day}`;
    }

    // 显示日历弹窗
    function showCalendarModal() {
        calendarModal.classList.remove('hidden');

        // 使用淡入动画
        setTimeout(() => {
            calendarModal.style.opacity = '1';
        }, 10);

        // 生成当前月份的日历
        generateCalendar(currentMonth, currentYear);
    }

    // 隐藏日历弹窗
    function hideCalendarModal() {
        // 使用淡出动画
        calendarModal.style.opacity = '0';

        // 等待动画完成后隐藏
        setTimeout(() => {
            calendarModal.classList.add('hidden');
        }, 250);
    }

    // 农历计算辅助函数
    function lunarYearDays(year) {
        let sum = 348;
        for (let i = 0x8000; i > 0x8; i >>= 1) {
            sum += (lunarInfo[year - 1900] & i) ? 1 : 0;
        }
        return sum + leapDays(year);
    }

    function leapDays(year) {
        if (leapMonth(year)) {
            return (lunarInfo[year - 1900] & 0x10000) ? 30 : 29;
        }
        return 0;
    }

    function leapMonth(year) {
        return lunarInfo[year - 1900] & 0xf;
    }

    function monthDays(year, month) {
        return (lunarInfo[year - 1900] & (0x10000 >> month)) ? 30 : 29;
    }

    // 获取农历日期
    function getLunarDate(date) {
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const day = date.getDate();

        // 检查是否是节气
        const solarTermKey = `${month}-${day}`;
        if (solarTerms[solarTermKey]) {
            return solarTerms[solarTermKey];
        }

        // 检查是否是传统节日
        if (traditionalHolidays[solarTermKey]) {
            return traditionalHolidays[solarTermKey].name;
        }

        // 计算农历日期
        let offset = Math.floor((date - lunarBaseDate) / 86400000);

        let lunarYear = 1900;
        let temp = 0;

        while (lunarYear < 2100 && offset > 0) {
            temp = lunarYearDays(lunarYear);
            offset -= temp;
            lunarYear++;
        }

        if (offset < 0) {
            offset += temp;
            lunarYear--;
        }

        let lunarMonth = 1;
        let leap = leapMonth(lunarYear);
        let isLeap = false;

        while (lunarMonth < 13 && offset > 0) {
            if (leap > 0 && lunarMonth === (leap + 1) && !isLeap) {
                lunarMonth--;
                isLeap = true;
                temp = leapDays(lunarYear);
            } else {
                temp = monthDays(lunarYear, lunarMonth);
            }

            if (isLeap && lunarMonth === (leap + 1)) {
                isLeap = false;
            }

            offset -= temp;
            lunarMonth++;
        }

        if (offset === 0 && leap > 0 && lunarMonth === leap + 1) {
            if (isLeap) {
                isLeap = false;
            } else {
                isLeap = true;
                lunarMonth--;
            }
        }

        if (offset < 0) {
            offset += temp;
            lunarMonth--;
        }

        const lunarDay = offset + 1;

        // 返回农历日期显示
        if (lunarDay === 1) {
            return lunarMonths[lunarMonth - 1] + '月';
        } else {
            return lunarDays[lunarDay - 1] || '初一';
        }
    }

    // 检查是否是节假日
    function isHoliday(date) {
        const month = date.getMonth() + 1;
        const day = date.getDate();
        const key = `${month}-${day}`;

        // 检查是否是公历节假日
        if (holidays[key] && holidays[key].isHoliday) {
            return true;
        }

        // 检查是否是传统节日（农历节假日）
        if (traditionalHolidays[key] && traditionalHolidays[key].isHoliday) {
            return true;
        }

        // 检查是否是周末（双休制：周六和周日都是休息日）
        const dayOfWeek = date.getDay();
        return dayOfWeek === 0 || dayOfWeek === 6; // 周六(6)和周日(0)都是休息日
    }

    // 生成日历
    function generateCalendar(month, year) {
        // 更新日历标题
        calendarMonthYear.textContent = `${year}年${month + 1}月`;

        // 清空日历
        calendarDays.innerHTML = '';

        // 获取当月第一天是星期几（0是周日，1是周一）
        let firstDay = new Date(year, month, 1).getDay();
        // 调整为周一开始（0是周一，6是周日）
        firstDay = firstDay === 0 ? 6 : firstDay - 1;

        // 获取当月的天数
        const daysInMonth = new Date(year, month + 1, 0).getDate();

        // 获取上个月的天数
        const prevMonthDays = new Date(year, month, 0).getDate();

        // 计算行数（通常是6行）
        const numRows = Math.ceil((firstDay + daysInMonth) / 7);

        // 当前日期计数器
        let date = 1;
        // 上个月日期计数器
        let prevMonthDate = prevMonthDays - firstDay + 1;
        // 下个月日期计数器
        let nextMonthDate = 1;

        // 生成日历表格
        for (let i = 0; i < numRows; i++) {
            // 创建行
            const row = document.createElement('tr');

            // 创建7列（周一到周日）
            for (let j = 0; j < 7; j++) {
                // 创建单元格
                const cell = document.createElement('td');
                cell.className = 'calendar-cell p-0';

                // 创建日期容器
                const dayEl = document.createElement('div');

                // 确定日期类型（上个月、当前月或下个月）
                if (i === 0 && j < firstDay) {
                    // 上个月的日期
                    dayEl.className = 'calendar-day other-month';

                    // 创建日期元素
                    const solarEl = document.createElement('div');
                    solarEl.className = 'calendar-day-solar';
                    solarEl.textContent = prevMonthDate;

                    // 创建农历元素
                    const lunarEl = document.createElement('div');
                    lunarEl.className = 'calendar-day-lunar';

                    // 计算上个月的日期
                    const prevMonth = month === 0 ? 11 : month - 1;
                    const prevYear = month === 0 ? year - 1 : year;
                    const prevDate = new Date(prevYear, prevMonth, prevMonthDate);

                    // 获取农历日期
                    lunarEl.textContent = getLunarDate(prevDate);

                    // 添加到日期单元格
                    dayEl.appendChild(solarEl);
                    dayEl.appendChild(lunarEl);

                    prevMonthDate++;
                } else if (date <= daysInMonth) {
                    // 当前月的日期
                    const currentDate = new Date(year, month, date);
                    const isCurrentDay = date === today.getDate() && month === today.getMonth() && year === today.getFullYear();
                    const isHolidayDay = isHoliday(currentDate);

                    // 设置日期样式
                    let className = 'calendar-day';

                    if (isCurrentDay) {
                        className += ' today';
                    }

                    if (isHolidayDay) {
                        className += ' holiday';
                    }

                    dayEl.className = className;

                    // 创建日期元素
                    const solarEl = document.createElement('div');
                    solarEl.className = 'calendar-day-solar';
                    solarEl.textContent = date;

                    // 创建农历元素
                    const lunarEl = document.createElement('div');
                    lunarEl.className = 'calendar-day-lunar';

                    // 获取农历日期
                    lunarEl.textContent = getLunarDate(currentDate);

                    // 添加到日期单元格
                    dayEl.appendChild(solarEl);
                    dayEl.appendChild(lunarEl);

                    // 添加点击事件
                    dayEl.addEventListener('click', () => {
                        // 可以在这里添加点击日期的功能
                        console.log(`选择了 ${year}年${month + 1}月${date}日`);
                    });

                    date++;
                } else {
                    // 下个月的日期
                    dayEl.className = 'calendar-day other-month';

                    // 创建日期元素
                    const solarEl = document.createElement('div');
                    solarEl.className = 'calendar-day-solar';
                    solarEl.textContent = nextMonthDate;

                    // 创建农历元素
                    const lunarEl = document.createElement('div');
                    lunarEl.className = 'calendar-day-lunar';

                    // 计算下个月的日期
                    const nextMonth = month === 11 ? 0 : month + 1;
                    const nextYear = month === 11 ? year + 1 : year;
                    const nextDate = new Date(nextYear, nextMonth, nextMonthDate);

                    // 获取农历日期
                    lunarEl.textContent = getLunarDate(nextDate);

                    // 添加到日期单元格
                    dayEl.appendChild(solarEl);
                    dayEl.appendChild(lunarEl);

                    nextMonthDate++;
                }

                // 将日期容器添加到单元格
                cell.appendChild(dayEl);

                // 将单元格添加到行
                row.appendChild(cell);
            }

            // 将行添加到表格
            calendarDays.appendChild(row);
        }
    }

    // 上个月按钮点击事件
    prevMonthBtn.addEventListener('click', () => {
        currentMonth--;
        if (currentMonth < 0) {
            currentMonth = 11;
            currentYear--;
        }
        generateCalendar(currentMonth, currentYear);
    });

    // 下个月按钮点击事件
    nextMonthBtn.addEventListener('click', () => {
        currentMonth++;
        if (currentMonth > 11) {
            currentMonth = 0;
            currentYear++;
        }
        generateCalendar(currentMonth, currentYear);
    });

    // 日历按钮点击事件
    calendarButton.addEventListener('click', () => {
        showCalendarModal();
    });

    // 关闭按钮点击事件
    if (calendarModalClose) {
        calendarModalClose.addEventListener('click', () => {
            hideCalendarModal();
        });
    }

    // 按ESC键关闭弹窗
    document.addEventListener('keydown', e => {
        if (e.key === 'Escape' && !calendarModal.classList.contains('hidden')) {
            hideCalendarModal();
        }
    });

    // 初始化日历按钮
    initCalendarButton();
});
