/**
 * 内嵌网页模态窗口功能
 * 用于在弹窗中显示AI助理网站
 */
document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const iframeModal = document.getElementById('iframe-modal');
    const iframeContent = document.getElementById('iframe-content');
    const iframeLoading = document.getElementById('iframe-loading');
    const iframeTitle = document.getElementById('iframe-title');
    const iframeRefresh = document.getElementById('iframe-refresh');
    const iframeExternal = document.getElementById('iframe-external');
    const iframeClose = document.getElementById('iframe-close');

    let currentUrl = '';
    let currentTitle = '';

    // 为AI助理链接添加点击事件
    function initAIAssistantLinks() {
        const aiLinks = document.querySelectorAll('.ai-assistant-link');
        aiLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const url = this.getAttribute('href');
                const title = this.getAttribute('data-title') || '内嵌网页';
                openIframeModal(url, title);
            });
        });
    }

    // 打开内嵌网页模态窗口
    function openIframeModal(url, title) {
        currentUrl = url;
        currentTitle = title;

        // 设置标题
        if (iframeTitle) {
            iframeTitle.textContent = title;
        }

        // 显示模态窗口
        if (iframeModal) {
            iframeModal.classList.remove('hidden');
        }

        // 显示加载动画
        showLoading();

        // 加载iframe内容
        loadIframe(url);

        // 阻止页面滚动
        document.body.style.overflow = 'hidden';
    }

    // 关闭内嵌网页模态窗口
    function closeIframeModal() {
        if (iframeModal) {
            iframeModal.classList.add('hidden');
        }

        // 清空iframe
        if (iframeContent) {
            iframeContent.src = '';
        }

        // 恢复页面滚动
        document.body.style.overflow = '';

        currentUrl = '';
        currentTitle = '';
    }

    // 加载iframe内容
    function loadIframe(url) {
        if (!iframeContent) return;

        // 设置iframe源
        iframeContent.src = url;

        // iframe加载完成事件
        iframeContent.onload = function() {
            hideLoading();
        };

        // iframe加载错误事件
        iframeContent.onerror = function() {
            hideLoading();
            showError();
        };

        // 超时处理（10秒）
        setTimeout(() => {
            if (iframeLoading && !iframeLoading.classList.contains('hidden')) {
                hideLoading();
            }
        }, 10000);
    }

    // 显示加载动画
    function showLoading() {
        if (iframeLoading) {
            iframeLoading.style.display = 'flex';
        }
    }

    // 隐藏加载动画
    function hideLoading() {
        if (iframeLoading) {
            iframeLoading.style.display = 'none';
        }
    }

    // 显示错误信息
    function showError() {
        if (iframeLoading) {
            iframeLoading.innerHTML = `
                <div class="error-message">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="15" y1="9" x2="9" y2="15"></line>
                        <line x1="9" y1="9" x2="15" y2="15"></line>
                    </svg>
                    <div class="error-text">加载失败</div>
                    <div class="error-desc">该网站可能不支持内嵌显示</div>
                    <button class="error-btn" onclick="window.open('${currentUrl}', '_blank')">
                        在新窗口中打开
                    </button>
                </div>
            `;
            iframeLoading.style.display = 'flex';
        }
    }

    // 刷新iframe
    function refreshIframe() {
        if (currentUrl) {
            showLoading();
            loadIframe(currentUrl);
        }
    }

    // 在新窗口打开
    function openExternal() {
        if (currentUrl) {
            window.open(currentUrl, '_blank');
        }
    }

    // 绑定控制按钮事件
    function bindControlEvents() {
        // 刷新按钮
        if (iframeRefresh) {
            iframeRefresh.addEventListener('click', refreshIframe);
        }

        // 外部打开按钮
        if (iframeExternal) {
            iframeExternal.addEventListener('click', openExternal);
        }

        // 关闭按钮
        if (iframeClose) {
            iframeClose.addEventListener('click', closeIframeModal);
        }

        // 点击遮罩层关闭
        if (iframeModal) {
            iframeModal.addEventListener('click', function(e) {
                if (e.target === iframeModal) {
                    closeIframeModal();
                }
            });
        }

        // ESC键关闭
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && !iframeModal.classList.contains('hidden')) {
                closeIframeModal();
            }
        });
    }

    // 添加错误消息样式
    function addErrorStyles() {
        if (document.getElementById('iframe-error-styles')) return;

        const style = document.createElement('style');
        style.id = 'iframe-error-styles';
        style.textContent = `
            .error-message {
                display: flex;
                flex-direction: column;
                align-items: center;
                text-align: center;
                color: var(--text-secondary);
                gap: 1rem;
            }

            .error-message svg {
                color: var(--danger, #ff4757);
                opacity: 0.7;
            }

            .error-text {
                font-size: 1.25rem;
                font-weight: 600;
                color: var(--text-primary);
            }

            .error-desc {
                font-size: 0.875rem;
                color: var(--text-secondary);
                opacity: 0.8;
            }

            .error-btn {
                margin-top: 0.5rem;
                padding: 0.5rem 1rem;
                background: var(--primary);
                color: white;
                border: none;
                border-radius: var(--radius-md);
                cursor: pointer;
                font-size: 0.875rem;
                transition: all var(--transition-normal);
            }

            .error-btn:hover {
                background: var(--primary-hover, var(--primary));
                transform: translateY(-1px);
            }
        `;
        document.head.appendChild(style);
    }

    // 初始化功能
    function init() {
        initAIAssistantLinks();
        bindControlEvents();
        addErrorStyles();
    }

    // 启动初始化
    init();

    // 导出函数供全局使用
    window.IframeModal = {
        open: openIframeModal,
        close: closeIframeModal,
        refresh: refreshIframe
    };
}); 