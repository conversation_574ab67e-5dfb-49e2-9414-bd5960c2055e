/**
 * Tooltip 系统 - 只支持点击显示
 */
document.addEventListener('DOMContentLoaded', function() {
    const tooltipContainer = document.getElementById('tooltip-container');
    const tooltipOverlay = document.getElementById('tooltip-overlay');
    let activeButton = null;

    // 确保tooltip容器存在
    if (!tooltipContainer) {
        console.warn('Tooltip container not found');
        return;
    }

    // 为所有 info-btn 添加点击事件
    document.querySelectorAll('.info-btn').forEach(btn => {
        // 移除任何可能的鼠标悬停事件和属性
        btn.removeAttribute('title');

        // 移除所有可能的悬停事件监听器
        btn.onmouseenter = null;
        btn.onmouseover = null;
        btn.onmouseleave = null;
        btn.onmouseout = null;

        // 克隆元素来移除所有事件监听器，然后重新添加点击事件
        const newBtn = btn.cloneNode(true);
        btn.parentNode.replaceChild(newBtn, btn);

        // 为新按钮添加点击事件
        newBtn.addEventListener('click', e => {
            e.preventDefault();
            e.stopPropagation();

            const tooltipContent = newBtn.getAttribute('data-tooltip');
            if (!tooltipContent) return;

            // 切换 tooltip 显示状态
            if (activeButton === newBtn && tooltipContainer.classList.contains('visible')) {
                hideTooltip();
                return;
            }

            activeButton = newBtn;
            tooltipContainer.innerHTML = tooltipContent;
            positionTooltip(newBtn);
            showTooltip();
        });
    });

    // 定位 tooltip
    function positionTooltip(button) {
        const buttonRect = button.getBoundingClientRect();
        const viewportWidth = window.innerWidth;

        tooltipContainer.classList.remove('arrow-left', 'arrow-right');

        let left = buttonRect.right + 15;
        let top = buttonRect.top;

        // 检查是否会超出右边界
        if (left + 250 > viewportWidth - 20) {
            left = buttonRect.left - 250 - 15;
            tooltipContainer.classList.add('arrow-right');
        } else {
            tooltipContainer.classList.add('arrow-left');
        }

        tooltipContainer.style.left = left + 'px';
        tooltipContainer.style.top = top + 'px';
    }

    // 显示 tooltip
    function showTooltip() {
        tooltipContainer.classList.add('visible');
        if (tooltipOverlay) {
            tooltipOverlay.classList.add('visible');
        }
    }

    // 隐藏 tooltip
    function hideTooltip() {
        tooltipContainer.classList.remove('visible');
        if (tooltipOverlay) {
            tooltipOverlay.classList.remove('visible');
        }
        activeButton = null;
    }

    // 事件监听
    tooltipOverlay.addEventListener('click', hideTooltip);

    document.addEventListener('keydown', e => {
        if (e.key === 'Escape' && tooltipContainer.classList.contains('visible')) {
            hideTooltip();
        }
    });

    window.addEventListener('resize', () => {
        if (activeButton && tooltipContainer.classList.contains('visible')) {
            positionTooltip(activeButton);
        }
    });
});
