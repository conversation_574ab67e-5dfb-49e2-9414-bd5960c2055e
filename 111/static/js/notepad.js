/**
 * 记事本功能
 */
document.addEventListener('DOMContentLoaded', function() {
    // 记事本功能实现
    const notepadButton = document.getElementById('notepad-button');
    const notepadContainer = document.getElementById('notepad-container');
    const notepadText = document.getElementById('notepad-text');
    const notepadSave = document.getElementById('notepad-save');
    const notepadClear = document.getElementById('notepad-clear');
    const notepadClose = document.getElementById('notepad-close');

    // 从本地存储加载记事本内容
    const savedNotepadContent = localStorage.getItem('notepadContent');
    if (savedNotepadContent) {
        notepadText.value = savedNotepadContent;
    }

    // 打开记事本
    notepadButton.addEventListener('click', function() {
        notepadContainer.classList.remove('hidden');

        // 使用淡入动画
        setTimeout(() => {
            notepadContainer.style.opacity = '1';
        }, 10);

        // 聚焦到文本区域
        setTimeout(() => {
            notepadText.focus();
        }, 100);
    });

    // 保存记事本内容
    notepadSave.addEventListener('click', function() {
        localStorage.setItem('notepadContent', notepadText.value);
        showNotification('记事本内容已保存', 2000, 'success');
    });

    // 清空记事本内容
    notepadClear.addEventListener('click', function() {
        if (confirm('确定要清空记事本内容吗？')) {
            notepadText.value = '';
            localStorage.removeItem('notepadContent');
        }
    });

    // 关闭记事本
    notepadClose.addEventListener('click', function() {
        // 自动保存内容
        localStorage.setItem('notepadContent', notepadText.value);

        // 使用淡出动画
        notepadContainer.style.opacity = '0';
        setTimeout(() => {
            notepadContainer.classList.add('hidden');
        }, 250);
    });

    // 按ESC键关闭记事本
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && !notepadContainer.classList.contains('hidden')) {
            // 自动保存内容
            localStorage.setItem('notepadContent', notepadText.value);

            // 使用淡出动画
            notepadContainer.style.opacity = '0';
            setTimeout(() => {
                notepadContainer.classList.add('hidden');
            }, 250);
        }
    });

    // 自动保存功能 - 使用防抖优化
    let autoSaveTimeout;
    notepadText.addEventListener('input', () => {
        clearTimeout(autoSaveTimeout);
        autoSaveTimeout = setTimeout(() => {
            if (!notepadContainer.classList.contains('hidden')) {
                localStorage.setItem('notepadContent', notepadText.value);
            }
        }, 2000); // 2秒后自动保存
    });
});
