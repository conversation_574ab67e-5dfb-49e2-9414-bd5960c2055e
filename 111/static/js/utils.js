/**
 * 通用工具函数
 */

// 现代化通知函数 - 与界面风格统一
function showNotification(message, duration = 2000, type = 'success') {
    // 检查是否已存在通知容器
    let notificationContainer = document.getElementById('notification-container');

    // 如果不存在，创建一个
    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.id = 'notification-container';
        notificationContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            display: flex;
            flex-direction: column;
            gap: 12px;
            pointer-events: none;
        `;
        document.body.appendChild(notificationContainer);
    }

    // 创建新通知
    const notification = document.createElement('div');

    // 使用与现代卡片系统一致的样式
    notification.style.cssText = `
        background: var(--card-bg);
        border: 1px solid var(--card-border);
        border-radius: var(--radius-xl);
        padding: 1rem 1.5rem;
        backdrop-filter: blur(12px);
        -webkit-backdrop-filter: blur(12px);
        box-shadow: var(--shadow-lg);
        color: var(--text-primary);
        font-size: 0.9rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        min-width: 280px;
        max-width: 400px;
        opacity: 0;
        transform: translateX(100px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        pointer-events: auto;
        position: relative;
        overflow: hidden;
    `;

    // 根据类型设置不同的样式
    let iconSvg = '';
    let accentColor = '';

    switch(type) {
        case 'success':
            iconSvg = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>';
            accentColor = '#10b981';
            break;
        case 'error':
            iconSvg = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line></svg>';
            accentColor = '#ef4444';
            break;
        case 'warning':
            iconSvg = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>';
            accentColor = '#f59e0b';
            break;
        default:
            iconSvg = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="m9 12 2 2 4-4"></path></svg>';
            accentColor = '#6366f1';
    }

    // 添加左侧装饰条
    const accent = document.createElement('div');
    accent.style.cssText = `
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4px;
        background: ${accentColor};
        border-radius: var(--radius-xl) 0 0 var(--radius-xl);
    `;
    notification.appendChild(accent);

    // 添加图标
    const icon = document.createElement('div');
    icon.style.cssText = `
        color: ${accentColor};
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
    `;
    icon.innerHTML = iconSvg;
    notification.appendChild(icon);

    // 添加文本
    const text = document.createElement('span');
    text.textContent = message;
    text.style.cssText = `
        flex: 1;
        line-height: 1.4;
    `;
    notification.appendChild(text);

    // 添加关闭按钮
    const closeBtn = document.createElement('button');
    closeBtn.style.cssText = `
        background: none;
        border: none;
        color: var(--text-secondary);
        cursor: pointer;
        padding: 4px;
        border-radius: var(--radius-md);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        flex-shrink: 0;
    `;
    closeBtn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>';

    closeBtn.addEventListener('mouseenter', () => {
        closeBtn.style.background = 'var(--surface-hover)';
        closeBtn.style.color = 'var(--text-primary)';
    });

    closeBtn.addEventListener('mouseleave', () => {
        closeBtn.style.background = 'none';
        closeBtn.style.color = 'var(--text-secondary)';
    });

    closeBtn.addEventListener('click', () => {
        hideNotification(notification, notificationContainer);
    });

    notification.appendChild(closeBtn);

    // 添加到容器
    notificationContainer.appendChild(notification);

    // 触发进入动画
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 10);

    // 设置自动消失
    const autoHideTimer = setTimeout(() => {
        hideNotification(notification, notificationContainer);
    }, duration);

    // 鼠标悬停时暂停自动消失
    notification.addEventListener('mouseenter', () => {
        clearTimeout(autoHideTimer);
    });

    notification.addEventListener('mouseleave', () => {
        setTimeout(() => {
            hideNotification(notification, notificationContainer);
        }, 1000);
    });
}

// 隐藏通知的辅助函数
function hideNotification(notification, container) {
    notification.style.opacity = '0';
    notification.style.transform = 'translateX(100px)';

    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }

        // 如果容器为空，也移除容器
        if (container && container.children.length === 0) {
            container.remove();
        }
    }, 300);
}
