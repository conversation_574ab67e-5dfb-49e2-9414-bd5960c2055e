/* ===========================================
   导航页完整样式系统
   替换Tailwind CSS，保持视觉效果一致
   =========================================== */

/* ===========================================
   1. CSS Reset & 基础设置
   =========================================== */

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
    overscroll-behavior: none;
}

body {
    font-family: 'Outfit', 'Space Grotesk', system-ui, sans-serif;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background: var(--bg);
    color: var(--text-primary);
    overflow-x: hidden;
    position: relative;
}

/* 页面背景装饰 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 226, 0.1) 0%, transparent 50%);
    z-index: -1;
    pointer-events: none;
    opacity: 0.8;
}



/* ===========================================
   2. CSS变量系统（主题支持）
   =========================================== */

:root {
    /* 主题颜色 - 浅色模式（优化配色方案）*/
    --primary-light: #2563eb;
    --primary-hover-light: #1d4ed8;
    --secondary-light: #06b6d4;
    --accent-light: #f59e0b;
    --success-light: #10b981;
    --warning-light: #f59e0b;
    --danger-light: #ef4444;
    
    /* 增强色彩层次 */
    --primary-50-light: #eff6ff;
    --primary-100-light: #dbeafe;
    --primary-500-light: #3b82f6;
    --primary-600-light: #2563eb;
    --primary-700-light: #1d4ed8;
    
    /* 背景色 - 浅色模式（增强层次感）*/
    --bg-light: #ffffff;
    --bg-card-light: #fafbfc;
    --bg-card-hover-light: #f1f5f9;
    --bg-subtle-light: #f8fafc;
    --bg-muted-light: #f1f5f9;
    
    /* 文本色 - 浅色模式（优化对比度）*/
    --text-primary-light: #0f172a;
    --text-secondary-light: #334155;
    --text-muted-light: #64748b;
    --text-subtle-light: #94a3b8;
    
    /* 边框色 - 浅色模式（更细腻的层次）*/
    --border-light: #e2e8f0;
    --border-secondary-light: #cbd5e1;
    
    /* 主题颜色 - 深色模式（优化配色）*/
    --primary-dark: #3b82f6;
    --primary-hover-dark: #60a5fa;
    --secondary-dark: #22d3ee;
    --accent-dark: #fbbf24;
    --success-dark: #34d399;
    --warning-dark: #fbbf24;
    --danger-dark: #f87171;
    
    /* 增强色彩层次 - 深色模式 */
    --primary-50-dark: #1e293b;
    --primary-100-dark: #334155;
    --primary-500-dark: #3b82f6;
    --primary-600-dark: #2563eb;
    --primary-700-dark: #1d4ed8;
    
    /* 背景色 - 深色模式（更丰富的层次）*/
    --bg-dark: #0f172a;
    --bg-card-dark: #1e293b;
    --bg-card-hover-dark: #334155;
    --bg-subtle-dark: #1e293b;
    --bg-muted-dark: #334155;
    
    /* 文本色 - 深色模式（优化可读性）*/
    --text-primary-dark: #f8fafc;
    --text-secondary-dark: #e2e8f0;
    --text-muted-dark: #94a3b8;
    --text-subtle-dark: #64748b;
    
    /* 边框色 - 深色模式（更精细的边界）*/
    --border-dark: #475569;
    --border-secondary-dark: #334155;
    
    /* 阴影系统（增强立体感）*/
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.15);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.25), 0 2px 4px -1px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.35), 0 4px 6px -2px rgba(0, 0, 0, 0.25);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.45), 0 10px 10px -5px rgba(0, 0, 0, 0.35);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
    
    /* 光晕效果 */
    --glow-primary: 0 0 20px rgba(59, 130, 246, 0.3);
    --glow-secondary: 0 0 20px rgba(34, 211, 238, 0.3);
    --glow-accent: 0 0 20px rgba(251, 191, 36, 0.3);
    
    /* 圆角系统（现代化设计）*/
    --radius-sm: 0.25rem;    /* 4px */
    --radius-md: 0.5rem;     /* 8px */
    --radius-lg: 0.75rem;    /* 12px */
    --radius-xl: 1rem;       /* 16px */
    --radius-2xl: 1.5rem;    /* 24px */
    --radius-3xl: 2rem;      /* 32px */
    --radius-full: 9999px;
    
    /* 过渡效果系统（精细化控制）*/
    --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-spring: 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    
    /* 间距系统（8pt网格）*/
    --spacing-0: 0;
    --spacing-1: 0.125rem;   /* 2px */
    --spacing-2: 0.25rem;    /* 4px */
    --spacing-3: 0.375rem;   /* 6px */
    --spacing-4: 0.5rem;     /* 8px */
    --spacing-6: 0.75rem;    /* 12px */
    --spacing-8: 1rem;       /* 16px */
    --spacing-10: 1.25rem;   /* 20px */
    --spacing-12: 1.5rem;    /* 24px */
    --spacing-16: 2rem;      /* 32px */
    --spacing-20: 2.5rem;    /* 40px */
    --spacing-24: 3rem;      /* 48px */
    --spacing-32: 4rem;      /* 64px */
    
    /* Z-index 层级系统 */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
}

/* 浅色主题 */
html[data-theme="light"] {
    --primary: var(--primary-light);
    --primary-hover: var(--primary-hover-light);
    --secondary: var(--secondary-light);
    --accent: var(--accent-light);
    --success: var(--success-light);
    --warning: var(--warning-light);
    --danger: var(--danger-light);
    --bg: var(--bg-light);
    --bg-card: var(--bg-card-light);
    --bg-card-hover: var(--bg-card-hover-light);
    --text-primary: var(--text-primary-light);
    --text-secondary: var(--text-secondary-light);
    --text-muted: var(--text-muted-light);
    --border: var(--border-light);
    --border-secondary: var(--border-secondary-light);
}

/* 深色主题 */
html[data-theme="dark"] {
    --primary: var(--primary-dark);
    --primary-hover: var(--primary-hover-dark);
    --secondary: var(--secondary-dark);
    --accent: var(--accent-dark);
    --success: var(--success-dark);
    --warning: var(--warning-dark);
    --danger: var(--danger-dark);
    --bg: var(--bg-dark);
    --bg-card: var(--bg-card-dark);
    --bg-card-hover: var(--bg-card-hover-dark);
    --text-primary: var(--text-primary-dark);
    --text-secondary: var(--text-secondary-dark);
    --text-muted: var(--text-muted-dark);
    --border: var(--border-dark);
    --border-secondary: var(--border-secondary-dark);
}

/* ===========================================
   3. 通用工具类
   =========================================== */

/* 显示/隐藏 */
.hidden { display: none !important; }
.visible { visibility: visible; }
.invisible { visibility: hidden; }

/* 屏幕阅读器专用 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 滚动隐藏 */
.scroll-hidden {
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.scroll-hidden::-webkit-scrollbar {
    display: none;
}

/* 过渡效果 */
.transition-colors {
    transition: color var(--transition-fast), background-color var(--transition-fast), border-color var(--transition-fast);
}

/* ===========================================
   4. 布局系统
   =========================================== */

/* Flexbox 布局 */
.flex { display: flex; }
.flex-1 { flex: 1 1 0%; }
.flex-none { flex: none; }
.flex-wrap { flex-wrap: wrap; }
.flex-col { flex-direction: column; }

/* 对齐方式 */
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-end { justify-content: flex-end; }
.self-start { align-self: flex-start; }

/* 定位 */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* 容器 */
.container {
    top: 1rem;
    width: 100%;
    max-width: 1600px;
    margin: 0 auto;
    padding: 0 1rem;
    max-height: calc(100vh - 10rem);
}

/* ===========================================
   5. 尺寸系统
   =========================================== */

/* 宽度 */
.w-full { width: 100%; }
.w-10 { width: 2.5rem; }
.w-12 { width: 3rem; }
.w-400 { width: 400px; }
.w-450 { width: 450px; }
.w-500 { width: 500px; }
.w-1200 { width: 1200px; }
.w-1600 { width: 1600px; }

/* 高度 */
.h-full { height: 100%; }
.h-6 { height: 1.5rem; }
.h-10 { height: 2.5rem; }
.h-400 { height: 400px; }

/* 最大宽度 */
.max-w-90vw { max-width: 90vw; }
.max-w-1200 { max-width: 1200px; }
.max-w-1600 { max-width: 1600px; }

/* 最大高度 */
.max-h-60vh { max-height: 60vh; }
.max-h-80vh { max-height: 80vh; }

/* 最小宽度 */
.min-w-0 { min-width: 0; }
.min-w-400 { min-width: 400px; }

/* 特殊尺寸 */
.w-calc-full-432 { width: calc(100% - 432px); }

/* ===========================================
   6. 间距系统
   =========================================== */

/* 外边距 */
.m-0 { margin: 0; }
.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 0.75rem; }
.ml-4 { margin-left: 1rem; }
.mr-2 { margin-right: 0.5rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mx-auto { margin-left: auto; margin-right: auto; }

/* 内边距 */
.p-1 { padding: 0.25rem; }
.p-4 { padding: 1rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-5 { padding-top: 1.25rem; padding-bottom: 1.25rem; }
.pt-6 { padding-top: 1.5rem; }
.pb-3 { padding-bottom: 0.75rem; }

/* 间隙 */
.gap-8 { gap: 2rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }

/* ===========================================
   7. 颜色系统
   =========================================== */

/* 背景色 */
.bg-white { background-color: #ffffff; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-gray-200 { background-color: #e5e7eb; }
.bg-gray-700 { background-color: #374151; }
.bg-gray-800 { background-color: #1f2937; }

/* 文本色 */
.text-white { color: #ffffff; }
.text-gray-300 { color: #d1d5db; }
.text-gray-400 { color: #9ca3af; }
.text-gray-500 { color: #6b7280; }
.text-gray-700 { color: #374151; }
.text-gray-900 { color: #111827; }
.text-blue-400 { color: #60a5fa; }
.text-blue-500 { color: #3b82f6; }
.text-red-400 { color: #f87171; }
.text-red-500 { color: #ef4444; }

/* 边框色 */
.border-gray-200 { border-color: #e5e7eb; }
.border-gray-700 { border-color: #374151; }
.border-t { border-top-width: 1px; border-top-style: solid; }
.border-b { border-bottom-width: 1px; border-bottom-style: solid; }
.border-collapse { border-collapse: collapse; }

/* 悬停状态 */
.hover\:bg-gray-200:hover { background-color: #e5e7eb; }
.hover\:bg-gray-600:hover { background-color: #4b5563; }
.hover\:underline:hover { text-decoration: underline; }

/* ===========================================
   8. 字体系统
   =========================================== */

/* 字体大小 */
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }

/* 字体重量 */
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

/* 文本对齐 */
.text-center { text-align: center; }

/* 行高 */
.leading-relaxed { line-height: 1.625; }

/* ===========================================
   9. 装饰效果
   =========================================== */

/* 圆角 */
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-full { border-radius: var(--radius-full); }
.rounded-b-lg { border-bottom-left-radius: var(--radius-lg); border-bottom-right-radius: var(--radius-lg); }

/* 阴影 */
.shadow-2xl { box-shadow: var(--shadow-2xl); }

/* 溢出 */
.overflow-y-auto { overflow-y: auto; }

/* ===========================================
   10. 组件样式
   =========================================== */

/* 头部组件（现代化设计）*/
.header-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: var(--z-fixed);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    transition: all var(--transition-normal);
}

[data-theme="dark"] .header-bar {
    background: rgba(15, 23, 42, 0.95);
    border-bottom-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.2);
}

.header-container {
    max-width: 1600px;
    margin: 0 auto;
    padding: var(--spacing-12) var(--spacing-16);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    position: relative;
}

.header-title {
    font-size: 2.25rem;
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 50%, var(--accent) 100%);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-family: 'Space Grotesk', system-ui, sans-serif;
    letter-spacing: -0.04em;
    text-transform: uppercase;
    margin-bottom: 0;
    position: relative;
}

.header-title::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary), var(--secondary), var(--accent));
    border-radius: var(--radius-full);
    opacity: 0.6;
}



.header-buttons-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-6);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-4) var(--spacing-6);
    border-radius: var(--radius-2xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .header-buttons-container {
    background: rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.1);
}

/* 搜索组件（增强设计）*/
.search-container {
    position: relative;
    margin-left: auto;
    margin-right: var(--spacing-12);
    max-width: 280px;
    flex: 1;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
}

.search-input {
    width: 100%;
    padding: var(--spacing-8) var(--spacing-12) var(--spacing-8) var(--spacing-16);
    border: 1px solid transparent;
    border-radius: var(--radius-2xl);
    background: rgba(255, 255, 255, 0.8);
    color: var(--text-primary);
    font-size: 0.9rem;
    font-weight: 500;
    transition: all var(--transition-normal);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] .search-input {
    background: rgba(30, 41, 59, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1), 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

[data-theme="dark"] .search-input:focus {
    background: rgba(30, 41, 59, 0.95);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2), 0 4px 12px rgba(0, 0, 0, 0.3);
}

.search-input::placeholder {
    color: var(--text-muted);
    font-weight: 400;
}

.search-button {
    position: absolute;
    right: var(--spacing-4);
    top: 50%;
    transform: translateY(-50%);
    width: 2.25rem;
    height: 2.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background: var(--primary);
    color: white;
    cursor: pointer;
    border-radius: var(--radius-full);
    transition: all var(--transition-bounce);
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.3);
}

.search-button:hover {
    background: var(--primary-hover);
    transform: translateY(-50%) scale(1.1);
    box-shadow: var(--glow-primary), 0 4px 8px rgba(37, 99, 235, 0.4);
}

.search-button:active {
    transform: translateY(-50%) scale(0.95);
}

/* 主题切换按钮（精致设计）*/
.theme-toggle-header {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    border: none;
    border-radius: var(--radius-full);
    background: rgba(255, 255, 255, 0.9);
    color: var(--text-primary);
    cursor: pointer;
    transition: all var(--transition-bounce);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

[data-theme="dark"] .theme-toggle-header {
    background: rgba(30, 41, 59, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-toggle-header::before {
    content: '';
    position: absolute;
    inset: 0;
    background: conic-gradient(from 0deg, var(--primary), var(--secondary), var(--accent), var(--primary));
    border-radius: var(--radius-full);
    opacity: 0;
    transition: all var(--transition-normal);
    z-index: -1;
}

.theme-toggle-header:hover {
    transform: scale(1.1) rotate(180deg);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.theme-toggle-header:hover::before {
    opacity: 0.1;
}

.theme-toggle-header:active {
    transform: scale(0.9) rotate(180deg);
}

.theme-icon {
    width: 1.375rem;
    height: 1.375rem;
    transition: all var(--transition-spring);
}

/* 主容器 */
.outer-container {
    max-width: 1600px;
    margin: 8rem auto 0;
    padding: 0 1rem;
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    justify-content: space-between;
    align-items: flex-start;
}

.left-column {
    flex: 1;
    min-width: 0;
    max-width: 1200px;
    margin-top: 1rem;
}

.right-column {
    flex: none;
    width: 400px;
    min-width: 400px;
    max-width: 400px;
    position: sticky;
    top: 1rem;
    align-self: flex-start;
    overflow-y: auto;
    max-height: calc(100vh - 10rem);
    transition: all var(--transition-normal);
}

/* 模块组件（现代卡片设计）*/
.module {
    margin-bottom: var(--spacing-16);
    padding: var(--spacing-16);
    background: var(--bg-card);
    border: 1px solid var(--border);
    border-radius: var(--radius-3xl);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.module::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary), var(--secondary), var(--accent));
    opacity: 0;
    transition: all var(--transition-normal);
}

.module:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-4px);
    border-color: rgba(37, 99, 235, 0.2);
}

.module:hover::before {
    opacity: 1;
}

.module-title {
    display: block;
    padding: var(--spacing-6) var(--spacing-12);
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary);
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.08), rgba(34, 211, 238, 0.08));
    border-radius: var(--radius-xl);
    font-family: 'Space Grotesk', system-ui, sans-serif;
    letter-spacing: -0.025em;
    border: 1px solid rgba(37, 99, 235, 0.1);
    margin-bottom: var(--spacing-12);
}

.items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--spacing-12);
    padding: var(--spacing-4) 0;
}

.item {
    padding: var(--spacing-16);
    background: var(--bg);
    border: 1px solid var(--border);
    border-radius: var(--radius-2xl);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--secondary), var(--accent));
    opacity: 0;
    transition: all var(--transition-normal);
}

.item:hover {
    background: var(--bg-card-hover);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
    border-color: rgba(34, 211, 238, 0.3);
}

.item:hover::before {
    opacity: 1;
}

.item h2 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-8);
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.link-container {
    margin-top: var(--spacing-12);
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-6);
}

.link-container a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-4) var(--spacing-8);
    background: var(--bg-card);
    color: var(--text-primary);
    text-decoration: none;
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    transition: all var(--transition-bounce);
    font-size: 0.875rem;
    font-weight: 500;
    position: relative;
    overflow: hidden;
    text-align: center;
    white-space: nowrap;
}

.link-container a::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    opacity: 0;
    transition: all var(--transition-normal);
    z-index: -1;
}

.link-container a:hover {
    color: white;
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow-md);
    border-color: transparent;
}

.link-container a:hover::before {
    opacity: 1;
}

.link-container a:active {
    transform: translateY(0) scale(0.98);
}

/* 信息按钮（精致设计）*/
.info-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border: none;
    border-radius: var(--radius-full);
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.1), rgba(34, 211, 238, 0.1));
    color: var(--primary);
    cursor: pointer;
    transition: all var(--transition-bounce);
    font-size: 1rem;
    font-weight: 600;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(37, 99, 235, 0.2);
    box-shadow: var(--shadow-sm);
}

.info-btn::before {
    content: '';
    position: absolute;
    inset: 0;
    background: conic-gradient(from 45deg, var(--primary), var(--secondary), var(--accent), var(--primary));
    opacity: 0;
    transition: all var(--transition-normal);
    z-index: -1;
}

.info-btn:hover {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.15), rgba(34, 211, 238, 0.15));
    transform: scale(1.1) rotate(5deg);
    box-shadow: var(--shadow-md), var(--glow-primary);
    border-color: rgba(37, 99, 235, 0.4);
}

.info-btn:hover::before {
    opacity: 0.05;
}

.info-btn:active {
    transform: scale(0.95) rotate(5deg);
}

/* 弹窗组件（现代化设计）*/
.modal-overlay {
    position: fixed;
    inset: 0;
    z-index: var(--z-modal-backdrop);
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    transition: all var(--transition-normal);
}

.modal-content {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    max-width: 60rem;
    background: var(--bg-card);
    border: 1px solid var(--border);
    border-radius: var(--radius-3xl);
    box-shadow: var(--shadow-2xl);
    transition: all var(--transition-spring);
    overflow: hidden;
}

.modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary), var(--secondary), var(--accent));
}

/* 按钮组件（现代化设计）*/
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-6);
    padding: var(--spacing-10) var(--spacing-16);
    border: none;
    border-radius: var(--radius-xl);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-bounce);
    min-height: 3rem;
    position: relative;
    overflow: hidden;
    font-size: 0.9rem;
    letter-spacing: 0.025em;
}

.btn::before {
    content: '';
    position: absolute;
    inset: 0;
    background: inherit;
    transition: all var(--transition-normal);
    z-index: -1;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary), var(--primary-hover));
    color: white;
    box-shadow: var(--shadow-md), var(--glow-primary);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-hover), var(--primary));
    transform: translateY(-3px) scale(1.05);
    box-shadow: var(--shadow-lg), var(--glow-primary);
}

.btn-primary:active {
    transform: translateY(-1px) scale(0.98);
    box-shadow: var(--shadow-sm);
}

.btn-secondary {
    background: var(--bg-card);
    color: var(--text-primary);
    border: 1px solid var(--border);
    box-shadow: var(--shadow-sm);
}

.btn-secondary::before {
    background: linear-gradient(135deg, var(--bg-card-hover), var(--bg-card));
    opacity: 0;
}

.btn-secondary:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: var(--shadow-md);
    border-color: var(--primary);
    color: var(--primary);
}

.btn-secondary:hover::before {
    opacity: 1;
}

.btn-secondary:active {
    transform: translateY(-1px) scale(0.98);
}

/* 表单组件（现代化输入框）*/
.form-input {
    width: 100%;
    padding: var(--spacing-12) var(--spacing-16);
    border: 2px solid var(--border);
    border-radius: var(--radius-xl);
    background: var(--bg);
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 500;
    transition: all var(--transition-bounce);
    position: relative;
}

.form-input::placeholder {
    color: var(--text-muted);
    font-weight: 400;
    transition: all var(--transition-normal);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary);
    background: var(--bg-card);
    box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1), var(--shadow-lg);
    transform: translateY(-2px);
}

.form-input:focus::placeholder {
    color: var(--text-subtle);
    transform: translateY(-2px);
}



/* 辅助工具网格（精美设计）*/
.aux-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-6);
    margin-top: var(--spacing-6);
}

.aux-link {
    display: flex;
    align-items: center;
    padding: var(--spacing-6) var(--spacing-8);
    background: var(--bg-card);
    border: 1px solid var(--border);
    border-radius: var(--radius-xl);
    color: var(--text-primary);
    text-decoration: none;
    transition: all var(--transition-bounce);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.aux-link::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, 
        rgba(37, 99, 235, 0.1), 
        rgba(34, 211, 238, 0.1), 
        rgba(251, 191, 36, 0.1)
    );
    opacity: 0;
    transition: all var(--transition-normal);
    z-index: -1;
}

.aux-link:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow-lg);
    border-color: rgba(37, 99, 235, 0.3);
    color: var(--primary);
}

.aux-link:hover::before {
    opacity: 1;
}

.aux-link:active {
    transform: translateY(-1px) scale(0.98);
}

.aux-link-icon {
    margin-right: var(--spacing-8);
    font-size: 1.25rem;
    transition: all var(--transition-bounce);
}

.aux-link:hover .aux-link-icon {
    transform: scale(1.2) rotate(5deg);
}

.aux-link-text {
    font-size: 0.875rem;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    letter-spacing: 0.025em;
}

/* 尺寸类补充 */
.w-500 { width: 500px; }
.h-400 { height: 400px; }

/* ===========================================
   12. 内嵌网页模态窗口样式
   =========================================== */

.iframe-modal-content {
    position: fixed;
    top: 2%;
    left: 2%;
    right: 2%;
    bottom: 2%;
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
}

.iframe-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    background: var(--bg-card-hover);
    border-bottom: 1px solid var(--border);
}

.iframe-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.iframe-controls {
    display: flex;
    gap: 0.5rem;
}

.iframe-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border: none;
    border-radius: var(--radius-md);
    background: var(--bg);
    color: var(--text-primary);
    cursor: pointer;
    transition: all var(--transition-normal);
    border: 1px solid var(--border);
}

.iframe-btn:hover {
    background: var(--bg-card);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.iframe-body {
    flex: 1;
    position: relative;
    overflow: hidden;
}

.iframe-content {
    width: 100%;
    height: 100%;
    border: none;
    background: white;
}

.iframe-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: var(--bg-card);
    z-index: 10;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 3px solid var(--border);
    border-top: 3px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

.loading-text {
    color: var(--text-secondary);
    font-size: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* AI助理链接特殊样式 */
.ai-assistant-link {
    position: relative;
}

.ai-assistant-link::after {
    content: '🖥️';
    margin-left: 0.5rem;
    opacity: 0.7;
    font-size: 0.875rem;
}

.ai-assistant-link:hover::after {
    opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .iframe-modal-content {
        top: 1%;
        left: 1%;
        right: 1%;
        bottom: 1%;
    }
    
    .iframe-header {
        padding: 0.75rem 1rem;
    }
    
    .iframe-title {
        font-size: 1.125rem;
    }
    
    .iframe-btn {
        width: 2.25rem;
        height: 2.25rem;
    }
}

/* 间距类补充 */
.space-x-2 > * + * {
    margin-left: 0.5rem;
}

/* 溢出处理 */
.overflow-hidden { overflow: hidden; }

/* 文本样式补充 */
.resize-none { resize: none; }

/* ===========================================
   11. 特殊状态和动画
   =========================================== */

/* 同级元素状态 */
.peer:focus ~ .peer-focus\:outline-none {
    outline: none;
}

.peer:focus ~ .peer-focus\:ring-4 {
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

.peer:checked ~ .peer-checked\:bg-blue-600 {
    background-color: var(--primary);
}

.peer:checked ~ .peer-checked\:after\:translate-x-full::after {
    transform: translateX(100%);
}

/* 动画 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* ===========================================
   11. 重要信息弹窗专用样式
   =========================================== */

/* 强调文本颜色 - 红色 */
.text-emphasis-danger {
    color: var(--danger);
    font-weight: 500;
}

/* 强调文本颜色 - 蓝色链接 */
.text-emphasis-primary {
    color: var(--primary);
    transition: color var(--transition-normal);
}

.text-emphasis-primary:hover {
    color: var(--primary-hover);
}

/* 返回顶部按钮样式 */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary);
    color: white;
    border: none;
    border-radius: var(--radius-full);
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    transition: all var(--transition-normal);
    opacity: 0;
    visibility: hidden;
    transform: translateY(100px);
}

.back-to-top:hover {
    background-color: var(--primary-hover);
    transform: translateY(0) scale(1.1);
    box-shadow: var(--shadow-xl);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top svg {
    width: 1.5rem;
    height: 1.5rem;
}

/* ===========================================
   12. 响应式设计（增强移动端体验）
   =========================================== */

/* 平板设备优化 */
@media (max-width: 1024px) {
    .header-container {
        flex-wrap: wrap;
        gap: var(--spacing-8);
    }
    
    .search-container {
        order: 3;
        width: 100%;
        margin: var(--spacing-8) 0 0;
        max-width: none;
    }
    
    .outer-container {
        margin-top: 10rem;
        gap: var(--spacing-16);
        padding: 0 var(--spacing-12);
    }
    
    .right-column {
        width: 350px;
        min-width: 350px;
        top: 1rem;
        max-height: calc(100vh - 12rem);
    }
    
    .items {
        grid-template-columns: 1fr;
    }
    
    .aux-grid {
        grid-template-columns: 1fr;
    }
}

/* 移动设备优化 */
@media (max-width: 768px) {
    .outer-container {
        flex-direction: column;
        margin-top: 7rem;
        gap: var(--spacing-16);
        padding: 0 var(--spacing-8);
    }
    
    .left-column,
    .right-column {
        width: 100%;
        max-width: 100%;
        min-width: 0;
        margin-top: 0;
    }
    
    .right-column {
        position: static;
        top: auto;
        margin-top: 0;
        max-height: none;
    }
    
    .header-container {
        padding: var(--spacing-8) var(--spacing-12);
    }
    
    .header-title {
        font-size: 1.75rem;
        order: 1;
    }
    
    .header-buttons-container {
        order: 2;
        gap: var(--spacing-4);
        padding: var(--spacing-3) var(--spacing-4);
    }
    
    .search-container {
        order: 3;
        margin: var(--spacing-6) 0 0;
    }
    
    .theme-toggle-header {
        width: 2.5rem;
        height: 2.5rem;
    }
    
    .search-input {
        padding: var(--spacing-6) var(--spacing-10) var(--spacing-6) var(--spacing-12);
        font-size: 0.875rem;
    }
    
    .search-button {
        width: 2rem;
        height: 2rem;
        right: var(--spacing-3);
    }
    
    .module {
        padding: var(--spacing-16);
        border-radius: var(--radius-2xl);
        margin-bottom: var(--spacing-16);
    }
    
    .module-title {
        font-size: 1.25rem;
        padding: var(--spacing-6) var(--spacing-12);
    }
    
    .item {
        padding: var(--spacing-16);
    }
    
    .link-container {
        gap: var(--spacing-4);
    }
    
    .link-container a {
        padding: var(--spacing-4) var(--spacing-6);
        font-size: 0.8rem;
    }
    
    .aux-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }
    
    .aux-link {
        padding: var(--spacing-6) var(--spacing-8);
    }
}

/* 小屏手机优化 */
@media (max-width: 480px) {
    .header-title {
        font-size: 1.375rem;
        letter-spacing: -0.02em;
    }
    
    .header-title::after {
        height: 2px;
    }
    
    .header-container {
        padding: var(--spacing-6) var(--spacing-8);
    }
    
    .header-buttons-container {
        gap: var(--spacing-3);
        padding: var(--spacing-2) var(--spacing-3);
    }
    
    .theme-toggle-header {
        width: 2.25rem;
        height: 2.25rem;
    }
    
    .theme-icon {
        width: 1.125rem;
        height: 1.125rem;
    }
    
    .search-input {
        padding: var(--spacing-4) var(--spacing-8) var(--spacing-4) var(--spacing-10);
        font-size: 0.8rem;
    }
    
    .module {
        padding: var(--spacing-12);
        border-radius: var(--radius-xl);
    }
    
    .module-title {
        font-size: 1.125rem;
        padding: var(--spacing-4) var(--spacing-8);
        margin-bottom: var(--spacing-12);
    }
    
    .item {
        padding: var(--spacing-12);
    }
    
    .item h2 {
        font-size: 1rem;
        margin-bottom: var(--spacing-8);
    }
    
    .link-container {
        margin-top: var(--spacing-8);
        gap: var(--spacing-3);
    }
    
    .link-container a {
        padding: var(--spacing-3) var(--spacing-6);
        font-size: 0.8rem;
        border-radius: var(--radius-md);
    }
    
    .aux-link {
        padding: var(--spacing-4) var(--spacing-6);
        border-radius: var(--radius-lg);
    }
    
    .aux-link-text {
        font-size: 0.8rem;
    }
    
    .aux-link-icon {
        font-size: 1rem;
        margin-right: var(--spacing-4);
    }
    
    .info-btn {
        width: 2rem;
        height: 2rem;
        font-size: 0.875rem;
    }
    
    .btn {
        min-height: 2.5rem;
        padding: var(--spacing-8) var(--spacing-12);
        font-size: 0.875rem;
        border-radius: var(--radius-lg);
    }
    
    .form-input {
        padding: var(--spacing-10) var(--spacing-12);
        border-radius: var(--radius-lg);
        font-size: 0.9rem;
    }
}

/* 极小屏幕优化 */
@media (max-width: 360px) {
    .header-title {
        font-size: 1.125rem;
    }
    
    .outer-container {
        padding: 0 var(--spacing-6);
    }
    
    .module {
        padding: var(--spacing-10);
    }
    
    .item {
        padding: var(--spacing-10);
    }
    
             .link-container {
        gap: var(--spacing-2);
        margin-top: var(--spacing-6);
    }
}

/* ===========================================
   13. 高级动画效果和微交互
   =========================================== */

/* 页面载入动画 */
.module, .item, .aux-link {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
    transform: translateY(30px);
}

.module:nth-child(1) { animation-delay: 0.1s; }
.module:nth-child(2) { animation-delay: 0.2s; }
.module:nth-child(3) { animation-delay: 0.3s; }
.module:nth-child(4) { animation-delay: 0.4s; }
.module:nth-child(5) { animation-delay: 0.5s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 悬浮粒子效果 */
.header-title:hover::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(37, 99, 235, 0.1), transparent);
    animation: rotate 2s linear infinite;
    pointer-events: none;
    z-index: -1;
}

@keyframes rotate {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

/* 鼠标跟随效果 */
.module {
    position: relative;
    overflow: hidden;
}

.module::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle 100px at var(--mouse-x, 0) var(--mouse-y, 0), 
                                rgba(37, 99, 235, 0.03), transparent);
    opacity: 0;
    transition: opacity var(--transition-normal);
    pointer-events: none;
}

.module:hover::after {
    opacity: 1;
}

/* 数据加载动画 */
@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
}

.loading-skeleton {
    background: linear-gradient(90deg, 
        var(--bg-card) 25%, 
        var(--bg-card-hover) 37%, 
        var(--bg-card) 63%);
    background-size: 200px 100%;
    animation: shimmer 1.5s ease-in-out infinite;
}

/* 提示气泡动画 */
.tooltip-bubble {
    position: relative;
    animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

/* 按钮波纹效果 */
.btn, .aux-link, .link-container a {
    position: relative;
    overflow: hidden;
}

.btn::after, .aux-link::after, .link-container a::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.4s, height 0.4s;
    z-index: 0;
}

.btn:active::after, .aux-link:active::after, .link-container a:active::after {
    width: 300px;
    height: 300px;
}

/* 性能优化 */
.module, .item, .btn, .aux-link, .link-container a {
    will-change: transform;
    backface-visibility: hidden;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
}

/* 缓解动画 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .module, .item, .aux-link {
        animation: none;
        opacity: 1;
        transform: none;
    }
}

/* 滚动条美化 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-card);
    border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, var(--primary), var(--secondary));
    border-radius: var(--radius-full);
    border: 2px solid var(--bg-card);
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, var(--primary-hover), var(--secondary));
    box-shadow: var(--glow-primary);
}

::-webkit-scrollbar-corner {
    background: var(--bg-card);
}

/* 选择文本样式 */
::selection {
    background: var(--primary);
    color: white;
    text-shadow: none;
}

::-moz-selection {
    background: var(--primary);
    color: white;
    text-shadow: none;
} 