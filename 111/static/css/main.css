/* ===========================================
   导航页主样式文件
   统一的样式规范和元素定义
   =========================================== */

/* ===========================================
   1. 统一的元素规范
   =========================================== */

/* 标题字号和颜色规范 */
:root {
    /* 标题字号规范 */
    --title-h1: 2rem;       /* 32px - 主标题 */
    --title-h2: 1.5rem;     /* 24px - 二级标题 */
    --title-h3: 1.25rem;    /* 20px - 三级标题 */
    --title-h4: 1.125rem;   /* 18px - 四级标题 */
    
    /* 标题颜色规范 */
    --title-primary: var(--primary);
    --title-secondary: var(--text-primary);
    --title-accent: var(--accent);
    
    /* 文本颜色规范 */
    --text-primary: var(--text-primary);
    --text-secondary: var(--text-secondary);
    --text-muted: var(--text-muted);
    
    /* 背景颜色规范 */
    --bg-primary: var(--bg);
    --bg-secondary: var(--bg-card);
    --bg-accent: var(--bg-card-hover);
    
    /* 边框颜色规范 */
    --border-primary: var(--border);
    --border-secondary: color-mix(in oklch, var(--border) 50%, transparent);
    
    /* 间距规范 */
    --spacing-xs: 0.25rem;   /* 4px */
    --spacing-sm: 0.5rem;    /* 8px */
    --spacing-md: 1rem;      /* 16px */
    --spacing-lg: 1.5rem;    /* 24px */
    --spacing-xl: 2rem;      /* 32px */
    --spacing-xxl: 3rem;     /* 48px */
    
    /* 圆角规范 */
    --radius-sm: 0.25rem;    /* 4px */
    --radius-md: 0.5rem;     /* 8px */
    --radius-lg: 0.75rem;    /* 12px */
    --radius-xl: 1rem;       /* 16px */
    
    /* 阴影规范 */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.2);
    
    /* 过渡效果规范 */
    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;
}

/* 统一的标题样式 */
h1, .h1 {
    font-size: var(--title-h1);
    font-weight: 700;
    color: var(--title-primary);
    line-height: 1.2;
    margin-bottom: var(--spacing-lg);
}

h2, .h2 {
    font-size: var(--title-h2);
    font-weight: 600;
    color: var(--title-secondary);
    line-height: 1.3;
    margin-bottom: var(--spacing-md);
}

h3, .h3 {
    font-size: var(--title-h3);
    font-weight: 600;
    color: var(--title-accent);
    line-height: 1.4;
    margin-bottom: var(--spacing-sm);
}

h4, .h4 {
    font-size: var(--title-h4);
    font-weight: 500;
    color: var(--title-secondary);
    line-height: 1.4;
    margin-bottom: var(--spacing-sm);
}

/* 统一的按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    min-height: 2.5rem;
}

.btn-primary {
    background-color: var(--primary);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
}

.btn-secondary:hover {
    background-color: var(--bg-accent);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* 统一的卡片样式 */
.card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

/* ===========================================
   2. 日历组件样式
   =========================================== */

/* 日历弹窗样式 */
#calendar-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 600px;
    max-width: 90vw;
    z-index: 1001;
    opacity: 0;
    transition: opacity var(--transition-normal), transform var(--transition-normal);
}

#calendar-modal.modal-visible {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}

#calendar-modal.hidden {
    display: none;
}

/* 日历导航按钮 */
.calendar-nav-btn {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    transition: all var(--transition-fast);
    margin: 0 var(--spacing-xs);
    border: none;
    cursor: pointer;
}

.calendar-nav-btn:hover {
    background-color: var(--bg-accent);
    transform: scale(1.05);
}

.calendar-nav-btn:active {
    transform: scale(0.95);
}

/* 日历表格 */
.calendar-table {
    table-layout: fixed;
    border-spacing: var(--spacing-xs);
    border-collapse: separate;
    width: 100%;
}

.calendar-table th {
    height: 32px;
    font-weight: 500;
    font-size: var(--title-h4);
    color: var(--text-muted);
}

.calendar-header {
    text-align: center;
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 500;
    color: var(--text-muted);
    padding: 0.25rem;
}

.calendar-cell {
    padding: 0;
    text-align: center;
    vertical-align: middle;
    height: 70px;
}

.calendar-day {
    position: relative;
    height: 70px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xs);
    cursor: pointer;
}

.calendar-day:hover:not(.other-month) {
    background-color: var(--bg-accent);
}

.calendar-day-solar {
    font-size: 18px;
    font-weight: 600;
    line-height: 1.4;
}

.calendar-day-lunar {
    font-size: 12px;
    color: var(--text-muted);
    line-height: 1.2;
    margin-top: var(--spacing-xs);
}

.calendar-day.today {
    background-color: var(--primary);
    color: white;
    box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.3);
}

.calendar-day.today .calendar-day-lunar {
    color: rgba(255, 255, 255, 0.9);
}

.calendar-day.holiday .calendar-day-solar,
.calendar-day.holiday .calendar-day-lunar {
    color: var(--danger);
    font-weight: 600;
}

.calendar-day.other-month {
    opacity: 0.4;
}

.calendar-day.other-month:hover {
    background-color: transparent;
    cursor: default;
}

/* 日历按钮样式 */
#calendar-button {
    position: relative;
    width: 100px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-xl);
    background-color: var(--bg-secondary);
    transition: all var(--transition-fast);
    padding: 0 var(--spacing-sm);
    border: 1px solid var(--border-secondary);
}

#calendar-button:hover {
    background-color: var(--bg-accent);
    transform: scale(1.02);
}

.calendar-button-content {
    line-height: 1.3;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.calendar-date-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

#current-date {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

#current-day {
    font-size: 12px;
    color: var(--text-muted);
}

/* ===========================================
   3. 弹窗组件样式
   =========================================== */

/* 弹窗遮罩层 */
#modal-overlay {
    position: fixed;
    inset: 0;
    z-index: 99999;
    background-color: rgba(0, 0, 0, 0.5);
    transition: opacity var(--transition-normal);
    opacity: 0;
}

#modal-overlay.modal-overlay-visible {
    opacity: 1;
}

/* 信息弹窗样式 */
#info-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    z-index: 100000;
    width: 100%;
    max-width: 56rem;
    transform: translate(-50%, -55%);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
    background-color: var(--bg-secondary);
    padding: var(--spacing-lg);
    color: var(--text-primary);
    box-shadow: var(--shadow-xl);
    transition: all var(--transition-normal);
    opacity: 0;
}

#info-modal.modal-visible {
    opacity: 1;
    transform: translate(-50%, -50%);
}

/* 弹窗内容滚动条样式 */
#info-modal > div:nth-child(2)::-webkit-scrollbar {
    width: 6px;
}

#info-modal > div:nth-child(2)::-webkit-scrollbar-track {
    border-radius: var(--radius-sm);
    background-color: var(--bg-primary);
}

#info-modal > div:nth-child(2)::-webkit-scrollbar-thumb {
    border-radius: var(--radius-sm);
    background-color: var(--primary);
    opacity: 0.5;
}

#info-modal > div:nth-child(2)::-webkit-scrollbar-thumb:hover {
    background-color: var(--primary-hover);
    opacity: 0.7;
}

/* ===========================================
   4. 记事本组件样式
   =========================================== */

/* 记事本容器 */
#notepad-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 500px;
    max-width: 90vw;
    height: 400px;
    max-height: 80vh;
    z-index: 1000;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    box-shadow: var(--shadow-xl);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
    display: flex;
    flex-direction: column;
}

#notepad-container.notepad-visible {
    display: flex;
    flex-direction: column;
}

#notepad-container.hidden {
    display: none;
}

/* 记事本文本区域 */
#notepad-text {
    width: 100%;
    height: 100%;
    resize: none;
    border-radius: var(--radius-md);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    padding: var(--spacing-md);
    border: 1px solid var(--border-primary);
    font-family: 'JetBrains Mono', monospace;
    font-size: 1rem;
    line-height: 1.6;
}

#notepad-text:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px color-mix(in oklch, var(--primary) 20%, transparent);
}

/* 记事本滚动条样式 */
#notepad-text::-webkit-scrollbar {
    width: 6px;
}

#notepad-text::-webkit-scrollbar-track {
    border-radius: var(--radius-sm);
    background-color: var(--bg-secondary);
}

#notepad-text::-webkit-scrollbar-thumb {
    border-radius: var(--radius-sm);
    background-color: var(--primary);
    opacity: 0.5;
}

#notepad-text::-webkit-scrollbar-thumb:hover {
    background-color: var(--primary-hover);
    opacity: 0.7;
}



/* ===========================================
   5. 工具提示样式
   =========================================== */

/* 工具提示遮罩层 */
#tooltip-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 99998;
    display: none;
}

#tooltip-overlay.visible {
    display: block;
}

/* 工具提示容器 */
#tooltip-container {
    position: fixed;
    width: 250px;
    padding: var(--spacing-sm);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-primary);
    z-index: 99999;
    font-size: 14px;
    line-height: 1.5;
    display: none;
}

#tooltip-container.visible {
    display: block;
}

/* 工具提示箭头 */
#tooltip-container:before {
    content: "";
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: var(--bg-secondary);
    transform: rotate(45deg);
    border: 1px solid var(--border-primary);
}

#tooltip-container.arrow-left:before {
    top: 15px;
    left: -5px;
    border-right: none;
    border-top: none;
}

#tooltip-container.arrow-right:before {
    top: 15px;
    right: -5px;
    border-left: none;
    border-bottom: none;
}

/* ===========================================
   6. 装饰花朵样式
   =========================================== */

/* 装饰花朵容器 */
.flower-decoration {
    pointer-events: none;
    position: fixed;
    z-index: 50;
    opacity: 0.4;
    filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.3));
    transition: all var(--transition-slow);
}

.flower-decoration:hover {
    opacity: 0.6;
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.5));
}

/* 花朵SVG样式 */
.flower-svg {
    height: auto;
    max-height: 100%;
    width: auto;
    max-width: 100%;
    transform: scale(1.5);
}

/* 小花朵样式 */
.small-flower {
    max-height: 70px;
    max-width: 70px;
    opacity: 0.35;
}

/* 花朵位置和动画 */
.flower-content-1 {
    position: fixed;
    top: 10rem;
    left: 15%;
    z-index: 50;
    animation: floatAnimation 10s ease-in-out 0.5s infinite;
}

.flower-content-2 {
    position: fixed;
    top: 30%;
    right: 25%;
    z-index: 50;
    animation: floatAnimation 12s ease-in-out 1.5s infinite;
}

.flower-content-3 {
    position: fixed;
    bottom: 20%;
    left: 30%;
    z-index: 50;
    animation: floatAnimation 9s ease-in-out 2.5s infinite;
}

/* 花朵浮动动画 */
@keyframes floatAnimation {
    0% {
        transform: translateY(0) rotate(0deg);
    }
    50% {
        transform: translateY(-10px) rotate(5deg);
    }
    100% {
        transform: translateY(0) rotate(0deg);
    }
}

/* ===========================================
   7. 特殊组件样式
   =========================================== */

/* 自动化工具栏 */
#automation-tools-column {
    max-height: calc(100vh - 80px);
}

/* ===========================================
   8. 响应式设计
   =========================================== */

/* 平板设备 */
@media (max-width: 768px) {
    .flower-decoration {
        display: none !important;
    }
    
    #calendar-modal {
        width: 95vw;
        max-width: 95vw;
    }
    
    #notepad-container {
        width: 95vw;
        max-width: 95vw;
        height: 70vh;
        max-height: 70vh;
    }

}

/* 手机设备 */
@media (max-width: 480px) {
    :root {
        --title-h1: 1.5rem;     /* 24px */
        --title-h2: 1.25rem;    /* 20px */
        --title-h3: 1.125rem;   /* 18px */
        --title-h4: 1rem;       /* 16px */
        
        --spacing-md: 0.75rem;  /* 12px */
        --spacing-lg: 1rem;     /* 16px */
        --spacing-xl: 1.5rem;   /* 24px */
    }
    
    .calendar-day {
        height: 50px;
    }
    
    .calendar-day-solar {
        font-size: 16px;
    }
    
    .calendar-day-lunar {
        font-size: 10px;
    }
    
    #notepad-container {
        height: 60vh;
        max-height: 60vh;
    }
}

/* ===========================================
   9. 打印样式
   =========================================== */

@media print {
    .flower-decoration,
    #tooltip-overlay,
    #tooltip-container,
    #modal-overlay,
    #info-modal,
    #notepad-container {
        display: none !important;
    }
    
    .calendar-nav-btn {
        display: none !important;
    }

} 