version: '3'

services:
  web:
    build: .
    container_name: navigation-page
    restart: unless-stopped
    ports:
      - "80:80"  # 绑定到所有网络接口
    volumes:
      - ./static:/usr/share/nginx/html/static
      - ./templates:/usr/share/nginx/html/templates
    healthcheck:
      test: ["CMD", "wget", "-q", "-O", "/dev/null", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
