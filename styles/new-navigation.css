/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    overflow-x: hidden;
    height: 100vh;
    position: relative;
}

/* 背景容器 */
.background-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 25%, #fd79a8 50%, #a29bfe 75%, #6c5ce7 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.background-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 30%, rgba(255, 182, 193, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(173, 216, 230, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 70%, rgba(221, 160, 221, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 90% 80%, rgba(255, 218, 185, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 10% 90%, rgba(152, 251, 152, 0.3) 0%, transparent 50%);
    animation: floatingBubbles 20s ease-in-out infinite;
}

@keyframes floatingBubbles {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(120deg); }
    66% { transform: translateY(10px) rotate(240deg); }
}

.background-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(2px);
}

/* 主容器 */
.main-container {
    position: relative;
    z-index: 1;
    min-height: 100vh;
    padding: 40px 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
}

/* 搜索栏样式 */
.search-container {
    width: 100%;
    max-width: 600px;
    margin-bottom: 20px;
}

.search-box {
    position: relative;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.search-box:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.search-input {
    flex: 1;
    background: none;
    border: none;
    outline: none;
    padding: 16px 0;
    font-size: 16px;
    color: #333;
    font-weight: 400;
}

.search-input::placeholder {
    color: rgba(0, 0, 0, 0.5);
}

.search-icon {
    color: rgba(0, 0, 0, 0.6);
    margin-left: 10px;
}

/* 快捷导航栏 */
.quick-nav {
    display: flex;
    gap: 8px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    flex-wrap: wrap;
    justify-content: center;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border-radius: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.7);
    white-space: nowrap;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.nav-item.active {
    background: rgba(255, 255, 255, 0.25);
    color: #333;
}

.nav-icon {
    font-size: 16px;
}

/* 卡片容器 */
.cards-container {
    width: 100%;
    max-width: 1000px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.card-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 20px;
}

/* 网站卡片样式 */
.website-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.website-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.website-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.website-card:hover::before {
    opacity: 1;
}

.card-icon {
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    border-radius: 12px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
}

.card-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
}

.card-content {
    flex: 1;
    min-width: 0;
}

.card-content h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.card-content p {
    font-size: 13px;
    color: rgba(0, 0, 0, 0.6);
    font-weight: 400;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-container {
        padding: 20px 16px;
        gap: 20px;
    }
    
    .quick-nav {
        padding: 6px;
        gap: 4px;
    }
    
    .nav-item {
        padding: 8px 12px;
        font-size: 13px;
    }
    
    .nav-item span {
        display: none;
    }
    
    .card-row {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .website-card {
        padding: 16px;
    }
}

@media (max-width: 480px) {
    .search-box {
        padding: 0 16px;
    }
    
    .search-input {
        padding: 14px 0;
        font-size: 15px;
    }
}
