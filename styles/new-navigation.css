/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    overflow-x: hidden;
    min-height: 100vh;
    position: relative;
    background: #0f0f23;
    color: #e5e5e5;
}

/* 背景容器 */
.background-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
}

.background-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.1);
}

/* 主容器 */
.main-container {
    position: relative;
    z-index: 1;
    min-height: 100vh;
    padding: 40px 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
}

/* 搜索栏样式 */
.search-container {
    width: 100%;
    max-width: 600px;
    margin-bottom: 20px;
}

.search-box {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 25px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.search-box:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

.search-input {
    flex: 1;
    background: none;
    border: none;
    outline: none;
    padding: 16px 0;
    font-size: 16px;
    color: #e5e5e5;
    font-weight: 400;
}

.search-input::placeholder {
    color: rgba(229, 229, 229, 0.5);
}

.search-icon {
    color: rgba(229, 229, 229, 0.6);
    margin-left: 10px;
}

/* 分页导航栏 */
.page-nav {
    display: flex;
    gap: 8px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    flex-wrap: wrap;
    justify-content: center;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border-radius: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
    color: rgba(229, 229, 229, 0.7);
    white-space: nowrap;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.nav-item.active {
    background: rgba(255, 255, 255, 0.15);
    color: #e5e5e5;
}

.nav-icon {
    font-size: 16px;
}

/* 卡片容器 */
.cards-container {
    width: 100%;
    max-width: 1000px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 分页内容 */
.page-content {
    display: none;
    width: 100%;
}

.page-content.active {
    display: block;
}

.page-section {
    margin-bottom: 40px;
}

.section-title {
    font-size: 20px;
    font-weight: 600;
    color: #e5e5e5;
    margin-bottom: 20px;
    padding-left: 4px;
}

.card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

/* 网站卡片样式 */
.website-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.website-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.website-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.5);
    border-color: rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.08);
}

.website-card:hover::before {
    opacity: 1;
}

.card-icon {
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    font-size: 24px;
}

.card-content {
    flex: 1;
    min-width: 0;
}

.card-content h3 {
    font-size: 16px;
    font-weight: 600;
    color: #e5e5e5;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.card-content p {
    font-size: 13px;
    color: rgba(229, 229, 229, 0.6);
    font-weight: 400;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-container {
        padding: 20px 16px;
        gap: 20px;
    }
    
    .quick-nav {
        padding: 6px;
        gap: 4px;
    }
    
    .nav-item {
        padding: 8px 12px;
        font-size: 13px;
    }
    
    .nav-item span {
        display: none;
    }
    
    .card-row {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .website-card {
        padding: 16px;
    }
}

@media (max-width: 480px) {
    .search-box {
        padding: 0 16px;
    }
    
    .search-input {
        padding: 14px 0;
        font-size: 15px;
    }
}
