// 导航页交互功能
document.addEventListener('DOMContentLoaded', function() {
    
    // 搜索功能
    const searchInput = document.querySelector('.search-input');
    const searchBox = document.querySelector('.search-box');
    
    // 搜索框焦点效果
    searchInput.addEventListener('focus', function() {
        searchBox.style.background = 'rgba(255, 255, 255, 0.25)';
        searchBox.style.borderColor = 'rgba(255, 255, 255, 0.4)';
        searchBox.style.transform = 'translateY(-2px)';
        searchBox.style.boxShadow = '0 12px 40px rgba(0, 0, 0, 0.15)';
    });
    
    searchInput.addEventListener('blur', function() {
        searchBox.style.background = 'rgba(255, 255, 255, 0.15)';
        searchBox.style.borderColor = 'rgba(255, 255, 255, 0.2)';
        searchBox.style.transform = 'translateY(0)';
        searchBox.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.1)';
    });
    
    // 搜索功能
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            const query = this.value.trim();
            if (query) {
                // 默认使用Google搜索
                window.open(`https://www.google.com/search?q=${encodeURIComponent(query)}`, '_blank');
            }
        }
    });
    
    // 分页导航点击事件
    const navItems = document.querySelectorAll('.nav-item');
    const pageContents = document.querySelectorAll('.page-content');

    navItems.forEach(item => {
        item.addEventListener('click', function() {
            const targetPage = this.getAttribute('data-page');

            // 移除所有active类
            navItems.forEach(nav => nav.classList.remove('active'));
            pageContents.forEach(page => page.classList.remove('active'));

            // 添加active类到当前项和对应页面
            this.classList.add('active');
            const targetPageElement = document.getElementById(`page-${targetPage}`);
            if (targetPageElement) {
                targetPageElement.classList.add('active');
            }
        });
    });
    
    // 网站卡片点击事件
    const websiteCards = document.querySelectorAll('.website-card');

    websiteCards.forEach(card => {
        card.addEventListener('click', function() {
            const url = this.getAttribute('data-url');

            if (url && url !== '#') {
                window.open(url, '_blank');
            }
        });

        // 添加键盘导航支持
        card.setAttribute('tabindex', '0');
        card.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    });
    
    // 添加卡片悬停音效（可选）
    websiteCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            // 可以在这里添加悬停音效
            // playHoverSound();
        });
    });
    
    // 搜索建议功能（简单实现）
    const searchSuggestions = [
        'GitHub', 'Google', 'YouTube', 'Bilibili', 'ChatGPT', 
        'Pixiv', 'DuckDuckGo', 'Yandex', 'Bing', 'Kimi'
    ];
    
    searchInput.addEventListener('input', function() {
        const query = this.value.toLowerCase();
        if (query.length > 0) {
            const matches = searchSuggestions.filter(item => 
                item.toLowerCase().includes(query)
            );
            // 这里可以显示搜索建议
            console.log('搜索建议:', matches);
        }
    });
    
    // 添加平滑滚动效果
    document.documentElement.style.scrollBehavior = 'smooth';
    
    // 添加页面加载动画
    const cards = document.querySelectorAll('.website-card');
    const navItemsAll = document.querySelectorAll('.nav-item');
    
    // 延迟显示动画
    setTimeout(() => {
        searchBox.style.opacity = '1';
        searchBox.style.transform = 'translateY(0)';
    }, 100);
    
    setTimeout(() => {
        document.querySelector('.quick-nav').style.opacity = '1';
        document.querySelector('.quick-nav').style.transform = 'translateY(0)';
    }, 200);
    
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 300 + index * 100);
    });
    
    // 初始化动画状态
    searchBox.style.opacity = '0';
    searchBox.style.transform = 'translateY(20px)';
    searchBox.style.transition = 'all 0.6s ease';
    
    document.querySelector('.quick-nav').style.opacity = '0';
    document.querySelector('.quick-nav').style.transform = 'translateY(20px)';
    document.querySelector('.quick-nav').style.transition = 'all 0.6s ease';
    
    cards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'all 0.6s ease';
    });
    
    // 添加右键菜单禁用（可选）
    document.addEventListener('contextmenu', function(e) {
        // e.preventDefault(); // 取消注释以禁用右键菜单
    });
    
    // 添加快捷键支持
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K 聚焦搜索框
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            searchInput.focus();
        }
        
        // ESC 键取消搜索框焦点
        if (e.key === 'Escape') {
            searchInput.blur();
        }
    });
    
    console.log('导航页已加载完成');
});
